const mongoose = require('mongoose');

/**
 * Quiz Schema
 */
const quizSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User is required']
  },
  category: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category',
    required: [true, 'Category is required']
  },
  questions: [{
    question: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Question',
      required: true
    },
    userAnswer: {
      type: String,
      default: null
    },
    isCorrect: {
      type: Boolean,
      default: false
    },
    timeSpent: {
      type: Number,
      default: 0 // in seconds
    },
    points: {
      type: Number,
      default: 0
    },
    aiEvaluation: {
      feedback: String,
      confidence: Number,
      suggestions: [String]
    }
  }],
  settings: {
    difficulty: {
      type: String,
      enum: ['easy', 'medium', 'hard', 'mixed'],
      default: 'mixed'
    },
    timeLimit: {
      type: Number,
      default: 30 // seconds per question
    },
    questionCount: {
      type: Number,
      default: 10,
      min: [5, 'Minimum 5 questions required'],
      max: [50, 'Maximum 50 questions allowed']
    },
    randomOrder: {
      type: Boolean,
      default: true
    }
  },
  results: {
    totalQuestions: {
      type: Number,
      default: 0
    },
    correctAnswers: {
      type: Number,
      default: 0
    },
    totalScore: {
      type: Number,
      default: 0
    },
    percentage: {
      type: Number,
      default: 0
    },
    totalTimeSpent: {
      type: Number,
      default: 0 // in seconds
    },
    averageTimePerQuestion: {
      type: Number,
      default: 0
    },
    grade: {
      type: String,
      enum: ['A+', 'A', 'B+', 'B', 'C+', 'C', 'D', 'F'],
      default: 'F'
    }
  },
  status: {
    type: String,
    enum: ['in-progress', 'completed', 'abandoned'],
    default: 'in-progress'
  },
  startedAt: {
    type: Date,
    default: Date.now
  },
  completedAt: {
    type: Date,
    default: null
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Indexes for better query performance
quizSchema.index({ user: 1, createdAt: -1 });
quizSchema.index({ category: 1 });
quizSchema.index({ status: 1 });
quizSchema.index({ 'results.totalScore': -1 });

// Pre-save middleware
quizSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Instance method to calculate results
quizSchema.methods.calculateResults = function() {
  const totalQuestions = this.questions.length;
  const correctAnswers = this.questions.filter(q => q.isCorrect).length;
  const totalScore = this.questions.reduce((sum, q) => sum + q.points, 0);
  const totalTimeSpent = this.questions.reduce((sum, q) => sum + q.timeSpent, 0);
  
  this.results.totalQuestions = totalQuestions;
  this.results.correctAnswers = correctAnswers;
  this.results.totalScore = totalScore;
  this.results.percentage = totalQuestions > 0 ? (correctAnswers / totalQuestions) * 100 : 0;
  this.results.totalTimeSpent = totalTimeSpent;
  this.results.averageTimePerQuestion = totalQuestions > 0 ? totalTimeSpent / totalQuestions : 0;
  
  // Calculate grade based on percentage
  const percentage = this.results.percentage;
  if (percentage >= 97) this.results.grade = 'A+';
  else if (percentage >= 93) this.results.grade = 'A';
  else if (percentage >= 87) this.results.grade = 'B+';
  else if (percentage >= 83) this.results.grade = 'B';
  else if (percentage >= 77) this.results.grade = 'C+';
  else if (percentage >= 73) this.results.grade = 'C';
  else if (percentage >= 60) this.results.grade = 'D';
  else this.results.grade = 'F';
  
  return this;
};

// Instance method to complete quiz
quizSchema.methods.completeQuiz = function() {
  this.status = 'completed';
  this.completedAt = new Date();
  this.calculateResults();
  return this.save();
};

// Static method to get user quiz history
quizSchema.statics.getUserHistory = function(userId, page = 1, limit = 10) {
  const skip = (page - 1) * limit;
  
  return this.find({ user: userId })
    .populate('category', 'name slug icon color')
    .select('category results status startedAt completedAt')
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit);
};

// Static method to get quiz statistics
quizSchema.statics.getQuizStats = function(userId = null) {
  const matchQuery = userId ? { user: mongoose.Types.ObjectId(userId) } : {};
  
  return this.aggregate([
    { $match: matchQuery },
    {
      $group: {
        _id: null,
        totalQuizzes: { $sum: 1 },
        completedQuizzes: {
          $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] }
        },
        averageScore: { $avg: '$results.totalScore' },
        averagePercentage: { $avg: '$results.percentage' },
        totalTimeSpent: { $sum: '$results.totalTimeSpent' }
      }
    }
  ]);
};

// Static method to get leaderboard by category
quizSchema.statics.getCategoryLeaderboard = function(categoryId, limit = 10) {
  return this.aggregate([
    { 
      $match: { 
        category: mongoose.Types.ObjectId(categoryId),
        status: 'completed'
      }
    },
    {
      $group: {
        _id: '$user',
        bestScore: { $max: '$results.totalScore' },
        averageScore: { $avg: '$results.totalScore' },
        totalQuizzes: { $sum: 1 }
      }
    },
    {
      $lookup: {
        from: 'users',
        localField: '_id',
        foreignField: '_id',
        as: 'userInfo'
      }
    },
    { $unwind: '$userInfo' },
    {
      $project: {
        username: '$userInfo.username',
        fullName: { $concat: ['$userInfo.firstName', ' ', '$userInfo.lastName'] },
        avatar: '$userInfo.avatar',
        bestScore: 1,
        averageScore: 1,
        totalQuizzes: 1
      }
    },
    { $sort: { bestScore: -1 } },
    { $limit: limit }
  ]);
};

// Virtual for duration
quizSchema.virtual('duration').get(function() {
  if (this.completedAt && this.startedAt) {
    return Math.round((this.completedAt - this.startedAt) / 1000); // in seconds
  }
  return 0;
});

// Ensure virtual fields are serialized
quizSchema.set('toJSON', {
  virtuals: true
});

module.exports = mongoose.model('Quiz', quizSchema);
