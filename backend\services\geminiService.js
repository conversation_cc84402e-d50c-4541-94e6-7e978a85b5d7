const { GoogleGenerativeAI } = require('@google/generative-ai');

/**
 * Gemini Service for AI-powered answer evaluation and feedback
 */
class GeminiService {
  constructor() {
    if (!process.env.GEMINI_API_KEY) {
      console.warn('⚠️ Gemini API key not found. Gemini features will be disabled.');
      this.client = null;
      return;
    }

    this.client = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
    this.model = this.client.getGenerativeModel({ model: 'gemini-pro' });
    
    this.generationConfig = {
      temperature: 0.7,
      topK: 1,
      topP: 1,
      maxOutputTokens: 1000,
    };
  }

  /**
   * Check if Gemini service is available
   */
  isAvailable() {
    return this.client !== null;
  }

  /**
   * Evaluate user's answer with detailed feedback
   */
  async evaluateAnswer(question, userAnswer, correctAnswer, context = {}) {
    if (!this.isAvailable()) {
      throw new Error('Gemini service is not available. Please check your API key.');
    }

    try {
      const prompt = this.buildEvaluationPrompt(question, userAnswer, correctAnswer, context);
      
      const result = await this.model.generateContent({
        contents: [{ role: 'user', parts: [{ text: prompt }] }],
        generationConfig: this.generationConfig,
      });

      const response = await result.response;
      const content = response.text();

      if (!content) {
        throw new Error('No response received from Gemini');
      }

      return this.parseEvaluationResponse(content);
    } catch (error) {
      console.error('Gemini answer evaluation error:', error);
      throw new Error(`Failed to evaluate answer: ${error.message}`);
    }
  }

  /**
   * Generate personalized feedback based on user's quiz performance
   */
  async generatePersonalizedFeedback(quizResults, userStats) {
    if (!this.isAvailable()) {
      throw new Error('Gemini service is not available. Please check your API key.');
    }

    try {
      const prompt = this.buildFeedbackPrompt(quizResults, userStats);
      
      const result = await this.model.generateContent({
        contents: [{ role: 'user', parts: [{ text: prompt }] }],
        generationConfig: this.generationConfig,
      });

      const response = await result.response;
      const content = response.text();

      return this.parseFeedbackResponse(content);
    } catch (error) {
      console.error('Gemini feedback generation error:', error);
      throw new Error(`Failed to generate feedback: ${error.message}`);
    }
  }

  /**
   * Generate study recommendations based on weak areas
   */
  async generateStudyRecommendations(weakAreas, category) {
    if (!this.isAvailable()) {
      throw new Error('Gemini service is not available. Please check your API key.');
    }

    try {
      const prompt = this.buildStudyPrompt(weakAreas, category);
      
      const result = await this.model.generateContent({
        contents: [{ role: 'user', parts: [{ text: prompt }] }],
        generationConfig: this.generationConfig,
      });

      const response = await result.response;
      const content = response.text();

      return this.parseStudyRecommendations(content);
    } catch (error) {
      console.error('Gemini study recommendations error:', error);
      return [];
    }
  }

  /**
   * Analyze quiz patterns and provide insights
   */
  async analyzeQuizPatterns(userQuizHistory) {
    if (!this.isAvailable()) {
      throw new Error('Gemini service is not available. Please check your API key.');
    }

    try {
      const prompt = this.buildAnalysisPrompt(userQuizHistory);
      
      const result = await this.model.generateContent({
        contents: [{ role: 'user', parts: [{ text: prompt }] }],
        generationConfig: this.generationConfig,
      });

      const response = await result.response;
      const content = response.text();

      return this.parseAnalysisResponse(content);
    } catch (error) {
      console.error('Gemini pattern analysis error:', error);
      return null;
    }
  }

  /**
   * Build prompt for answer evaluation
   */
  buildEvaluationPrompt(question, userAnswer, correctAnswer, context) {
    return `As an educational AI assistant, evaluate this quiz answer and provide detailed feedback:

Question: ${question}
User's Answer: ${userAnswer}
Correct Answer: ${correctAnswer}
${context.category ? `Category: ${context.category}` : ''}
${context.difficulty ? `Difficulty: ${context.difficulty}` : ''}

Please provide a comprehensive evaluation including:
1. Whether the answer is correct, partially correct, or incorrect
2. Detailed explanation of why the answer is right/wrong
3. Key concepts the user should understand
4. Specific suggestions for improvement
5. Confidence score (0-100) for your evaluation

Format your response as JSON:
{
  "isCorrect": boolean,
  "accuracy": "correct|partial|incorrect",
  "feedback": "Detailed feedback explanation",
  "keyPoints": ["key concept 1", "key concept 2"],
  "suggestions": ["suggestion 1", "suggestion 2"],
  "confidence": number,
  "encouragement": "Positive encouragement message"
}`;
  }

  /**
   * Build prompt for personalized feedback
   */
  buildFeedbackPrompt(quizResults, userStats) {
    return `Analyze this user's quiz performance and provide personalized feedback:

Quiz Results:
- Score: ${quizResults.percentage}% (${quizResults.correctAnswers}/${quizResults.totalQuestions})
- Time: ${Math.round(quizResults.totalTimeSpent / 60)} minutes
- Category: ${quizResults.category}
- Difficulty: ${quizResults.difficulty}

User Statistics:
- Total Quizzes: ${userStats.totalQuizzes}
- Average Score: ${userStats.averageScore}%
- Best Score: ${userStats.bestScore}%
- Total Time Spent: ${Math.round(userStats.totalTimeSpent / 60)} hours

Provide personalized feedback including:
1. Performance assessment
2. Strengths identified
3. Areas for improvement
4. Motivational message
5. Next steps recommendations

Format as JSON:
{
  "assessment": "Overall performance evaluation",
  "strengths": ["strength 1", "strength 2"],
  "improvements": ["area 1", "area 2"],
  "motivation": "Encouraging message",
  "nextSteps": ["recommendation 1", "recommendation 2"]
}`;
  }

  /**
   * Build prompt for study recommendations
   */
  buildStudyPrompt(weakAreas, category) {
    return `Generate study recommendations for a student who needs improvement in these areas:

Weak Areas: ${weakAreas.join(', ')}
Subject Category: ${category}

Provide specific, actionable study recommendations including:
1. Key topics to focus on
2. Study methods and techniques
3. Practice exercises or activities
4. Recommended resources
5. Time allocation suggestions

Format as a simple array of recommendation strings.`;
  }

  /**
   * Build prompt for quiz pattern analysis
   */
  buildAnalysisPrompt(userQuizHistory) {
    const historyText = userQuizHistory.map(quiz => 
      `${quiz.category}: ${quiz.score}% in ${quiz.timeSpent}s`
    ).join('\n');

    return `Analyze this user's quiz performance patterns:

Quiz History:
${historyText}

Identify:
1. Performance trends
2. Strong and weak categories
3. Time management patterns
4. Learning progress indicators
5. Recommendations for improvement

Provide insights in JSON format:
{
  "trends": "Performance trend analysis",
  "strongCategories": ["category1", "category2"],
  "weakCategories": ["category1", "category2"],
  "timeManagement": "Time usage analysis",
  "progress": "Learning progress assessment",
  "recommendations": ["rec1", "rec2"]
}`;
  }

  /**
   * Parse evaluation response
   */
  parseEvaluationResponse(content) {
    try {
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        return {
          feedback: content.trim(),
          confidence: 80,
          suggestions: []
        };
      }

      const evaluation = JSON.parse(jsonMatch[0]);
      return {
        isCorrect: evaluation.isCorrect || false,
        accuracy: evaluation.accuracy || 'incorrect',
        feedback: evaluation.feedback || content.trim(),
        keyPoints: evaluation.keyPoints || [],
        suggestions: evaluation.suggestions || [],
        confidence: evaluation.confidence || 80,
        encouragement: evaluation.encouragement || ''
      };
    } catch (error) {
      console.error('Error parsing Gemini evaluation:', error);
      return {
        feedback: content.trim(),
        confidence: 70,
        suggestions: []
      };
    }
  }

  /**
   * Parse feedback response
   */
  parseFeedbackResponse(content) {
    try {
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        return { assessment: content.trim() };
      }

      return JSON.parse(jsonMatch[0]);
    } catch (error) {
      console.error('Error parsing Gemini feedback:', error);
      return { assessment: content.trim() };
    }
  }

  /**
   * Parse study recommendations
   */
  parseStudyRecommendations(content) {
    try {
      // Try to extract array or list from response
      const lines = content.split('\n').filter(line => line.trim());
      return lines.map(line => line.replace(/^\d+\.\s*/, '').replace(/^-\s*/, '').trim());
    } catch (error) {
      console.error('Error parsing study recommendations:', error);
      return [content.trim()];
    }
  }

  /**
   * Parse analysis response
   */
  parseAnalysisResponse(content) {
    try {
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        return null;
      }

      return JSON.parse(jsonMatch[0]);
    } catch (error) {
      console.error('Error parsing Gemini analysis:', error);
      return null;
    }
  }
}

module.exports = new GeminiService();
