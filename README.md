# 🧠 AI Quiz Master

A comprehensive full-stack quiz website powered by ChatGPT and Gemini AI, built with Node.js, Express.js, MongoDB, and modern frontend technologies.

## ✨ Features

### 🤖 AI-Powered Intelligence
- **ChatGPT Integration**: Dynamic question generation based on topics and difficulty levels
- **Gemini AI Evaluation**: Smart answer evaluation with detailed feedback and suggestions
- **Adaptive Learning**: AI-driven personalized recommendations and insights

### 🎯 Core Functionality
- **User Authentication**: Secure registration, login, and profile management
- **Multiple Categories**: Science, General Knowledge, Programming, Mathematics, and more
- **Real-time Quizzes**: Interactive quiz interface with timers and instant scoring
- **Progress Tracking**: Comprehensive analytics and performance dashboards
- **Leaderboards**: Global and category-specific competitive rankings
- **Admin Panel**: Complete management system for users, categories, and questions

### 🎨 Modern UI/UX
- **Responsive Design**: Fully optimized for desktop, tablet, and mobile devices
- **Dark/Light Mode**: Seamless theme switching with user preference persistence
- **Smooth Animations**: Beautiful transitions and loading states
- **Tailwind CSS**: Modern, utility-first styling approach

### 🔧 Technical Excellence
- **RESTful API**: Clean, well-documented backend architecture
- **MongoDB Integration**: Efficient data storage and retrieval
- **JWT Authentication**: Secure token-based authentication system
- **Error Handling**: Comprehensive error management and user feedback
- **Performance Optimized**: Fast loading times and smooth interactions

## 🚀 Quick Start

### Prerequisites
- Node.js (v16 or higher)
- MongoDB (local or MongoDB Atlas)
- OpenAI API Key (for ChatGPT integration)
- Google AI API Key (for Gemini integration)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ai-quiz-website
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   
   Create a `.env` file in the root directory:
   ```env
   # Server Configuration
   PORT=5000
   NODE_ENV=development
   
   # Database Configuration
   MONGODB_URI=mongodb://localhost:27017/ai-quiz-website
   # For MongoDB Atlas: mongodb+srv://username:<EMAIL>/ai-quiz-website
   
   # JWT Configuration
   JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
   JWT_EXPIRE=7d
   
   # API Keys
   OPENAI_API_KEY=your-openai-api-key-here
   GEMINI_API_KEY=your-gemini-api-key-here
   
   # Frontend URL (for CORS)
   FRONTEND_URL=http://localhost:3000
   ```

4. **Start the application**
   ```bash
   # Development mode (with auto-restart)
   npm run dev
   
   # Production mode
   npm start
   ```

5. **Access the application**
   - Backend API: `http://localhost:5000`
   - Frontend: Open `frontend/public/index.html` in your browser or serve with a static server

### API Keys Setup

#### OpenAI API Key (ChatGPT)
1. Visit [OpenAI Platform](https://platform.openai.com/)
2. Create an account or sign in
3. Navigate to API Keys section
4. Create a new API key
5. Add it to your `.env` file as `OPENAI_API_KEY`

#### Google AI API Key (Gemini)
1. Visit [Google AI Studio](https://makersuite.google.com/)
2. Create a new project or select existing one
3. Generate an API key
4. Add it to your `.env` file as `GEMINI_API_KEY`

## 📁 Project Structure

```
ai-quiz-website/
├── backend/
│   ├── config/
│   │   ├── database.js          # Database configuration
│   │   └── config.js            # App configuration
│   ├── models/
│   │   ├── User.js              # User model
│   │   ├── Quiz.js              # Quiz model
│   │   ├── Question.js          # Question model
│   │   └── Category.js          # Category model
│   ├── routes/
│   │   ├── auth.js              # Authentication routes
│   │   ├── quiz.js              # Quiz routes
│   │   ├── user.js              # User routes
│   │   └── admin.js             # Admin routes
│   ├── middleware/
│   │   ├── auth.js              # Authentication middleware
│   │   ├── validation.js        # Input validation
│   │   └── errorHandler.js      # Error handling
│   ├── services/
│   │   ├── chatgptService.js    # ChatGPT integration
│   │   ├── geminiService.js     # Gemini AI integration
│   │   └── quizService.js       # Quiz business logic
│   └── server.js                # Main server file
├── frontend/
│   └── public/
│       ├── index.html           # Homepage
│       ├── login.html           # Login page
│       ├── register.html        # Registration page
│       ├── dashboard.html       # User dashboard
│       ├── quiz.html            # Quiz interface
│       ├── leaderboard.html     # Leaderboards
│       ├── admin.html           # Admin panel
│       ├── styles/
│       │   ├── main.css         # Main styles
│       │   └── animations.css   # Animation styles
│       └── js/
│           ├── main.js          # Core JavaScript
│           ├── auth.js          # Authentication logic
│           ├── quiz.js          # Quiz functionality
│           ├── dashboard.js     # Dashboard logic
│           └── admin.js         # Admin panel logic
├── package.json
├── .env
└── README.md
```

## 🔌 API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `GET /api/auth/me` - Get current user
- `PUT /api/auth/profile` - Update profile
- `PUT /api/auth/change-password` - Change password

### Quiz Management
- `GET /api/quiz/categories` - Get all categories
- `POST /api/quiz/start` - Start a new quiz
- `POST /api/quiz/:quizId/answer/:questionId` - Submit answer
- `POST /api/quiz/:quizId/complete` - Complete quiz
- `GET /api/quiz/history` - Get quiz history
- `GET /api/quiz/leaderboard/:categoryId` - Get leaderboard

### User Features
- `GET /api/user/dashboard` - Get dashboard data
- `GET /api/user/stats` - Get user statistics
- `GET /api/user/recommendations` - Get AI recommendations
- `GET /api/user/achievements` - Get user achievements

### Admin Panel
- `GET /api/admin/dashboard` - Admin dashboard
- `GET /api/admin/users` - Manage users
- `GET /api/admin/categories` - Manage categories
- `POST /api/admin/categories` - Create category
- `GET /api/admin/questions` - Manage questions
- `POST /api/admin/questions/generate` - Generate AI questions

## 🎮 Usage Guide

### For Users
1. **Registration**: Create an account with email and password
2. **Category Selection**: Choose from various quiz categories
3. **Take Quizzes**: Answer questions with real-time feedback
4. **Track Progress**: Monitor your performance and improvements
5. **Compete**: Check leaderboards and compete with others

### For Admins
1. **User Management**: View and manage user accounts
2. **Category Management**: Create and organize quiz categories
3. **Question Management**: Add questions manually or generate with AI
4. **Analytics**: Monitor platform usage and performance

## 🛠️ Development

### Available Scripts
```bash
npm start          # Start production server
npm run dev        # Start development server with nodemon
npm run client     # Serve frontend with live-server
npm test           # Run tests (to be implemented)
```

### Database Setup
The application will automatically connect to MongoDB using the connection string in your `.env` file. Make sure MongoDB is running locally or provide a MongoDB Atlas connection string.

### Adding New Features
1. **Backend**: Add routes in `backend/routes/`, models in `backend/models/`
2. **Frontend**: Create new HTML pages and corresponding JavaScript files
3. **Styling**: Use Tailwind CSS classes or add custom styles in `styles/main.css`

## 🔒 Security Features

- **JWT Authentication**: Secure token-based authentication
- **Password Hashing**: Bcrypt for secure password storage
- **Input Validation**: Comprehensive validation using express-validator
- **Rate Limiting**: API rate limiting to prevent abuse
- **CORS Configuration**: Proper cross-origin resource sharing setup
- **Helmet.js**: Security headers for enhanced protection

## 🌟 AI Integration Details

### ChatGPT Features
- Dynamic question generation based on category and difficulty
- Contextual question creation with proper formatting
- Explanation generation for educational value

### Gemini AI Features
- Intelligent answer evaluation beyond simple matching
- Detailed feedback with suggestions for improvement
- Personalized learning recommendations
- Performance pattern analysis

## 📱 Responsive Design

The application is fully responsive and optimized for:
- **Desktop**: Full-featured experience with all functionality
- **Tablet**: Optimized layout with touch-friendly interactions
- **Mobile**: Streamlined interface perfect for on-the-go learning

## 🎨 Theming

- **Light Mode**: Clean, bright interface for daytime use
- **Dark Mode**: Easy-on-the-eyes design for low-light environments
- **Auto-switching**: Respects system preferences
- **Persistent**: User choice saved across sessions

## 🚀 Deployment

### Production Checklist
1. Set `NODE_ENV=production` in environment variables
2. Use strong JWT secret key
3. Configure MongoDB Atlas for cloud database
4. Set up proper CORS origins
5. Enable HTTPS in production
6. Configure rate limiting appropriately
7. Set up monitoring and logging

### Recommended Hosting
- **Backend**: Heroku, DigitalOcean, AWS, or Vercel
- **Database**: MongoDB Atlas
- **Frontend**: Netlify, Vercel, or serve from backend

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **OpenAI** for ChatGPT API
- **Google** for Gemini AI API
- **Tailwind CSS** for the utility-first CSS framework
- **Font Awesome** for beautiful icons
- **MongoDB** for the flexible database solution

## 📞 Support

For support, email <EMAIL> or create an issue in the repository.

---

**Built with ❤️ using Node.js, Express.js, MongoDB, ChatGPT, and Gemini AI**
