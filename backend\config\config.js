/**
 * Application configuration
 */
const config = {
  // Server configuration
  port: process.env.PORT || 5000,
  nodeEnv: process.env.NODE_ENV || 'development',
  
  // Database configuration
  mongoUri: process.env.MONGODB_URI || 'mongodb://localhost:27017/ai-quiz-website',
  
  // JWT configuration
  jwtSecret: process.env.JWT_SECRET || 'fallback-secret-key',
  jwtExpire: process.env.JWT_EXPIRE || '7d',
  
  // API Keys
  openaiApiKey: process.env.OPENAI_API_KEY,
  geminiApiKey: process.env.GEMINI_API_KEY,
  
  // Frontend configuration
  frontendUrl: process.env.FRONTEND_URL || 'http://localhost:3000',
  
  // Rate limiting
  rateLimitWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000,
  rateLimitMaxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
  
  // Quiz configuration
  quiz: {
    defaultTimeLimit: 30, // seconds per question
    maxQuestions: 20,
    difficultyLevels: ['easy', 'medium', 'hard'],
    categories: [
      'Science',
      'General Knowledge',
      'Programming',
      'Mathematics',
      'History',
      'Geography',
      'Sports',
      'Entertainment',
      'Technology',
      'Literature'
    ]
  },
  
  // AI configuration
  ai: {
    chatgpt: {
      model: 'gpt-3.5-turbo',
      maxTokens: 1000,
      temperature: 0.7
    },
    gemini: {
      model: 'gemini-pro',
      maxTokens: 1000,
      temperature: 0.7
    }
  },
  
  // Email configuration (for future features)
  email: {
    host: process.env.EMAIL_HOST,
    port: process.env.EMAIL_PORT,
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS
  }
};

/**
 * Validate required environment variables
 */
const validateConfig = () => {
  const requiredVars = ['MONGODB_URI', 'JWT_SECRET'];
  const missingVars = requiredVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    console.error('❌ Missing required environment variables:', missingVars.join(', '));
    console.error('Please check your .env file');
    process.exit(1);
  }
  
  // Warn about missing API keys
  if (!config.openaiApiKey) {
    console.warn('⚠️ OPENAI_API_KEY not found. ChatGPT features will be disabled.');
  }
  
  if (!config.geminiApiKey) {
    console.warn('⚠️ GEMINI_API_KEY not found. Gemini features will be disabled.');
  }
};

module.exports = {
  config,
  validateConfig
};
