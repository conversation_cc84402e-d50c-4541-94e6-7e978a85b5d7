const express = require('express');
const Quiz = require('../models/Quiz');
const Question = require('../models/Question');
const Category = require('../models/Category');
const User = require('../models/User');
const { authenticate } = require('../middleware/auth');
const { validateQuizCreation, validateQuizAnswer, validatePagination, validateObjectId } = require('../middleware/validation');
const { asyncHandler } = require('../middleware/errorHandler');
const chatgptService = require('../services/chatgptService');
const geminiService = require('../services/geminiService');

const router = express.Router();

/**
 * @route   GET /api/quiz/categories
 * @desc    Get all active categories
 * @access  Public
 */
router.get('/categories', asyncHandler(async (req, res) => {
  const categories = await Category.getActiveCategories();

  res.json({
    success: true,
    data: {
      categories,
      count: categories.length
    }
  });
}));

/**
 * @route   POST /api/quiz/start
 * @desc    Start a new quiz
 * @access  Private
 */
router.post('/start', authenticate, validateQuizCreation, asyncHandler(async (req, res) => {
  const { category, settings = {} } = req.body;

  // Verify category exists
  const categoryDoc = await Category.findById(category);
  if (!categoryDoc || !categoryDoc.isActive) {
    return res.status(404).json({
      success: false,
      message: 'Category not found or inactive'
    });
  }

  // Get questions for the quiz
  const questionCount = settings.questionCount || 10;
  const difficulty = settings.difficulty || 'mixed';
  
  let questions;
  
  // Try to get AI-generated questions first, then fall back to database
  if (chatgptService.isAvailable() && Math.random() > 0.5) {
    try {
      const aiQuestions = await chatgptService.generateQuestions(
        categoryDoc.name, 
        difficulty === 'mixed' ? 'medium' : difficulty, 
        Math.min(questionCount, 5)
      );
      
      // Save AI questions to database for future use
      const savedQuestions = await Promise.all(
        aiQuestions.map(async (q) => {
          const question = new Question({
            ...q,
            category: categoryDoc._id,
            createdBy: req.user.id
          });
          return await question.save();
        })
      );
      
      questions = savedQuestions;
    } catch (error) {
      console.error('AI question generation failed, using database questions:', error);
    }
  }
  
  // If AI generation failed or not available, get from database
  if (!questions || questions.length === 0) {
    questions = await Question.getRandomQuestions(category, difficulty, questionCount);
  }

  if (questions.length === 0) {
    return res.status(404).json({
      success: false,
      message: 'No questions available for this category and difficulty'
    });
  }

  // Create new quiz
  const quiz = new Quiz({
    user: req.user.id,
    category,
    questions: questions.map(q => ({
      question: q._id,
      userAnswer: null,
      isCorrect: false,
      timeSpent: 0,
      points: 0
    })),
    settings: {
      difficulty,
      timeLimit: settings.timeLimit || 30,
      questionCount: questions.length,
      randomOrder: settings.randomOrder !== false
    }
  });

  await quiz.save();

  // Populate question details for response
  await quiz.populate('questions.question', 'question options timeLimit points type');
  await quiz.populate('category', 'name slug description icon color');

  res.status(201).json({
    success: true,
    message: 'Quiz started successfully',
    data: {
      quiz: {
        id: quiz._id,
        category: quiz.category,
        questions: quiz.questions.map(q => ({
          id: q.question._id,
          question: q.question.question,
          options: q.question.options,
          timeLimit: q.question.timeLimit,
          points: q.question.points,
          type: q.question.type
        })),
        settings: quiz.settings,
        status: quiz.status,
        startedAt: quiz.startedAt
      }
    }
  });
}));

/**
 * @route   POST /api/quiz/:quizId/answer/:questionId
 * @desc    Submit answer for a question
 * @access  Private
 */
router.post('/:quizId/answer/:questionId', 
  authenticate, 
  validateQuizAnswer, 
  asyncHandler(async (req, res) => {
    const { quizId, questionId } = req.params;
    const { answer, timeSpent } = req.body;

    // Find quiz and verify ownership
    const quiz = await Quiz.findOne({ _id: quizId, user: req.user.id })
      .populate('questions.question');

    if (!quiz) {
      return res.status(404).json({
        success: false,
        message: 'Quiz not found'
      });
    }

    if (quiz.status !== 'in-progress') {
      return res.status(400).json({
        success: false,
        message: 'Quiz is not in progress'
      });
    }

    // Find the question in the quiz
    const questionIndex = quiz.questions.findIndex(
      q => q.question._id.toString() === questionId
    );

    if (questionIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Question not found in this quiz'
      });
    }

    const quizQuestion = quiz.questions[questionIndex];
    const question = quizQuestion.question;

    // Check if already answered
    if (quizQuestion.userAnswer) {
      return res.status(400).json({
        success: false,
        message: 'Question already answered'
      });
    }

    // Evaluate answer
    const isCorrect = answer.toLowerCase().trim() === question.correctAnswer.toLowerCase().trim();
    const points = isCorrect ? question.points : 0;

    // Update quiz question
    quizQuestion.userAnswer = answer;
    quizQuestion.isCorrect = isCorrect;
    quizQuestion.timeSpent = timeSpent;
    quizQuestion.points = points;

    // Get AI evaluation if available
    if (geminiService.isAvailable()) {
      try {
        const aiEvaluation = await geminiService.evaluateAnswer(
          question.question,
          answer,
          question.correctAnswer,
          {
            category: quiz.category?.name || 'General',
            difficulty: question.difficulty
          }
        );
        quizQuestion.aiEvaluation = aiEvaluation;
      } catch (error) {
        console.error('AI evaluation failed:', error);
      }
    }

    // Update question stats
    await question.updateStats(isCorrect, timeSpent);

    await quiz.save();

    res.json({
      success: true,
      message: 'Answer submitted successfully',
      data: {
        isCorrect,
        points,
        correctAnswer: question.correctAnswer,
        explanation: question.explanation,
        aiEvaluation: quizQuestion.aiEvaluation
      }
    });
  })
);

/**
 * @route   POST /api/quiz/:quizId/complete
 * @desc    Complete a quiz
 * @access  Private
 */
router.post('/:quizId/complete', 
  authenticate, 
  validateObjectId('quizId'), 
  asyncHandler(async (req, res) => {
    const { quizId } = req.params;

    // Find quiz and verify ownership
    const quiz = await Quiz.findOne({ _id: quizId, user: req.user.id })
      .populate('category', 'name slug')
      .populate('questions.question', 'question correctAnswer explanation');

    if (!quiz) {
      return res.status(404).json({
        success: false,
        message: 'Quiz not found'
      });
    }

    if (quiz.status !== 'in-progress') {
      return res.status(400).json({
        success: false,
        message: 'Quiz is not in progress'
      });
    }

    // Complete the quiz
    await quiz.completeQuiz();

    // Update user stats
    const user = await User.findById(req.user.id);
    await user.updateStats(quiz.results.totalScore, Math.round(quiz.results.totalTimeSpent / 60));

    // Update category stats
    const category = await Category.findById(quiz.category._id);
    await category.updateStats(quiz.results.totalQuestions, quiz.results.percentage);

    res.json({
      success: true,
      message: 'Quiz completed successfully',
      data: {
        quiz: {
          id: quiz._id,
          category: quiz.category,
          results: quiz.results,
          questions: quiz.questions.map(q => ({
            question: q.question.question,
            userAnswer: q.userAnswer,
            correctAnswer: q.question.correctAnswer,
            isCorrect: q.isCorrect,
            points: q.points,
            timeSpent: q.timeSpent,
            explanation: q.question.explanation,
            aiEvaluation: q.aiEvaluation
          })),
          completedAt: quiz.completedAt,
          duration: quiz.duration
        }
      }
    });
  })
);

/**
 * @route   GET /api/quiz/history
 * @desc    Get user's quiz history
 * @access  Private
 */
router.get('/history', authenticate, validatePagination, asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;

  const quizzes = await Quiz.getUserHistory(req.user.id, page, limit);
  const totalQuizzes = await Quiz.countDocuments({ user: req.user.id });

  res.json({
    success: true,
    data: {
      quizzes,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(totalQuizzes / limit),
        totalQuizzes,
        hasNext: page < Math.ceil(totalQuizzes / limit),
        hasPrev: page > 1
      }
    }
  });
}));

/**
 * @route   GET /api/quiz/leaderboard/:categoryId
 * @desc    Get leaderboard for a category
 * @access  Public
 */
router.get('/leaderboard/:categoryId', 
  validateObjectId('categoryId'), 
  asyncHandler(async (req, res) => {
    const { categoryId } = req.params;
    const limit = parseInt(req.query.limit) || 10;

    const leaderboard = await Quiz.getCategoryLeaderboard(categoryId, limit);

    res.json({
      success: true,
      data: {
        leaderboard,
        count: leaderboard.length
      }
    });
  })
);

module.exports = router;
