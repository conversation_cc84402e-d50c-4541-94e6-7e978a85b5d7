const mongoose = require('mongoose');

/**
 * Category Schema
 */
const categorySchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Category name is required'],
    unique: true,
    trim: true,
    maxlength: [50, 'Category name cannot exceed 50 characters']
  },
  slug: {
    type: String,
    required: true,
    unique: true,
    lowercase: true
  },
  description: {
    type: String,
    required: [true, 'Category description is required'],
    maxlength: [200, 'Description cannot exceed 200 characters']
  },
  icon: {
    type: String,
    default: 'fas fa-question-circle'
  },
  color: {
    type: String,
    default: '#3B82F6' // Default blue color
  },
  isActive: {
    type: Boolean,
    default: true
  },
  difficulty: {
    easy: {
      type: Number,
      default: 0
    },
    medium: {
      type: Number,
      default: 0
    },
    hard: {
      type: Number,
      default: 0
    }
  },
  totalQuestions: {
    type: Number,
    default: 0
  },
  totalQuizzes: {
    type: Number,
    default: 0
  },
  averageScore: {
    type: Number,
    default: 0
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Index for better query performance
categorySchema.index({ slug: 1 });
categorySchema.index({ isActive: 1 });
categorySchema.index({ totalQuizzes: -1 });

// Pre-save middleware to generate slug
categorySchema.pre('save', function(next) {
  if (this.isModified('name')) {
    this.slug = this.name
      .toLowerCase()
      .replace(/[^a-zA-Z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '');
  }
  this.updatedAt = Date.now();
  next();
});

// Static method to get active categories
categorySchema.statics.getActiveCategories = function() {
  return this.find({ isActive: true })
    .select('name slug description icon color totalQuestions totalQuizzes averageScore')
    .sort({ totalQuizzes: -1 });
};

// Static method to get category stats
categorySchema.statics.getCategoryStats = function() {
  return this.aggregate([
    { $match: { isActive: true } },
    {
      $group: {
        _id: null,
        totalCategories: { $sum: 1 },
        totalQuestions: { $sum: '$totalQuestions' },
        totalQuizzes: { $sum: '$totalQuizzes' },
        averageScore: { $avg: '$averageScore' }
      }
    }
  ]);
};

// Instance method to update stats
categorySchema.methods.updateStats = function(questionsCount, quizScore) {
  this.totalQuestions += questionsCount;
  this.totalQuizzes += 1;
  
  // Update average score
  const totalScore = (this.averageScore * (this.totalQuizzes - 1)) + quizScore;
  this.averageScore = totalScore / this.totalQuizzes;
  
  return this.save();
};

module.exports = mongoose.model('Category', categorySchema);
