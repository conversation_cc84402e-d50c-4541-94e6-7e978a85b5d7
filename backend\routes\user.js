const express = require('express');
const User = require('../models/User');
const Quiz = require('../models/Quiz');
const { authenticate } = require('../middleware/auth');
const { validatePagination } = require('../middleware/validation');
const { asyncHandler } = require('../middleware/errorHandler');
const geminiService = require('../services/geminiService');

const router = express.Router();

/**
 * @route   GET /api/user/dashboard
 * @desc    Get user dashboard data
 * @access  Private
 */
router.get('/dashboard', authenticate, asyncHandler(async (req, res) => {
  const userId = req.user.id;

  // Get user stats
  const user = await User.findById(userId);
  
  // Get recent quiz history
  const recentQuizzes = await Quiz.getUserHistory(userId, 1, 5);
  
  // Get quiz statistics
  const quizStats = await Quiz.getQuizStats(userId);
  
  // Get performance by category
  const categoryPerformance = await Quiz.aggregate([
    { $match: { user: userId, status: 'completed' } },
    {
      $lookup: {
        from: 'categories',
        localField: 'category',
        foreignField: '_id',
        as: 'categoryInfo'
      }
    },
    { $unwind: '$categoryInfo' },
    {
      $group: {
        _id: '$category',
        categoryName: { $first: '$categoryInfo.name' },
        categorySlug: { $first: '$categoryInfo.slug' },
        categoryIcon: { $first: '$categoryInfo.icon' },
        categoryColor: { $first: '$categoryInfo.color' },
        totalQuizzes: { $sum: 1 },
        averageScore: { $avg: '$results.percentage' },
        bestScore: { $max: '$results.percentage' },
        totalTimeSpent: { $sum: '$results.totalTimeSpent' }
      }
    },
    { $sort: { totalQuizzes: -1 } }
  ]);

  // Calculate streaks and achievements
  const achievements = await calculateAchievements(user, quizStats[0]);

  res.json({
    success: true,
    data: {
      user: {
        id: user._id,
        username: user.username,
        fullName: user.fullName,
        avatar: user.avatar,
        stats: user.stats,
        preferences: user.preferences
      },
      recentQuizzes,
      quizStats: quizStats[0] || {
        totalQuizzes: 0,
        completedQuizzes: 0,
        averageScore: 0,
        averagePercentage: 0,
        totalTimeSpent: 0
      },
      categoryPerformance,
      achievements
    }
  });
}));

/**
 * @route   GET /api/user/stats
 * @desc    Get detailed user statistics
 * @access  Private
 */
router.get('/stats', authenticate, asyncHandler(async (req, res) => {
  const userId = req.user.id;

  // Get comprehensive stats
  const user = await User.findById(userId);
  const quizStats = await Quiz.getQuizStats(userId);
  
  // Get performance trends (last 30 days)
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  
  const performanceTrend = await Quiz.aggregate([
    {
      $match: {
        user: userId,
        status: 'completed',
        completedAt: { $gte: thirtyDaysAgo }
      }
    },
    {
      $group: {
        _id: {
          $dateToString: { format: '%Y-%m-%d', date: '$completedAt' }
        },
        quizCount: { $sum: 1 },
        averageScore: { $avg: '$results.percentage' },
        totalScore: { $sum: '$results.totalScore' }
      }
    },
    { $sort: { '_id': 1 } }
  ]);

  // Get difficulty breakdown
  const difficultyBreakdown = await Quiz.aggregate([
    { $match: { user: userId, status: 'completed' } },
    {
      $group: {
        _id: '$settings.difficulty',
        count: { $sum: 1 },
        averageScore: { $avg: '$results.percentage' }
      }
    }
  ]);

  res.json({
    success: true,
    data: {
      userStats: user.stats,
      quizStats: quizStats[0] || {},
      performanceTrend,
      difficultyBreakdown
    }
  });
}));

/**
 * @route   GET /api/user/leaderboard
 * @desc    Get global leaderboard
 * @access  Public
 */
router.get('/leaderboard', validatePagination, asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;

  const leaderboard = await User.getLeaderboard(limit);
  
  // Add rank to each user
  const rankedLeaderboard = leaderboard.map((user, index) => ({
    ...user.toObject(),
    rank: (page - 1) * limit + index + 1
  }));

  res.json({
    success: true,
    data: {
      leaderboard: rankedLeaderboard,
      count: rankedLeaderboard.length
    }
  });
}));

/**
 * @route   GET /api/user/recommendations
 * @desc    Get personalized study recommendations
 * @access  Private
 */
router.get('/recommendations', authenticate, asyncHandler(async (req, res) => {
  const userId = req.user.id;

  // Get user's quiz history to analyze weak areas
  const recentQuizzes = await Quiz.find({ 
    user: userId, 
    status: 'completed' 
  })
  .populate('category', 'name')
  .sort({ completedAt: -1 })
  .limit(20);

  if (recentQuizzes.length === 0) {
    return res.json({
      success: true,
      data: {
        recommendations: [],
        message: 'Complete some quizzes to get personalized recommendations!'
      }
    });
  }

  // Analyze performance to identify weak areas
  const categoryPerformance = {};
  recentQuizzes.forEach(quiz => {
    const categoryName = quiz.category.name;
    if (!categoryPerformance[categoryName]) {
      categoryPerformance[categoryName] = {
        totalQuizzes: 0,
        totalScore: 0,
        scores: []
      };
    }
    categoryPerformance[categoryName].totalQuizzes++;
    categoryPerformance[categoryName].totalScore += quiz.results.percentage;
    categoryPerformance[categoryName].scores.push(quiz.results.percentage);
  });

  // Identify weak areas (categories with average score < 70%)
  const weakAreas = Object.entries(categoryPerformance)
    .filter(([_, performance]) => {
      const averageScore = performance.totalScore / performance.totalQuizzes;
      return averageScore < 70;
    })
    .map(([category, _]) => category);

  let recommendations = [];

  // Get AI-powered recommendations if available
  if (geminiService.isAvailable() && weakAreas.length > 0) {
    try {
      const aiRecommendations = await geminiService.generateStudyRecommendations(
        weakAreas,
        weakAreas[0] // Focus on the first weak area
      );
      recommendations = aiRecommendations;
    } catch (error) {
      console.error('AI recommendations failed:', error);
    }
  }

  // Fallback recommendations
  if (recommendations.length === 0) {
    recommendations = generateFallbackRecommendations(categoryPerformance, weakAreas);
  }

  res.json({
    success: true,
    data: {
      recommendations,
      weakAreas,
      categoryPerformance: Object.entries(categoryPerformance).map(([category, perf]) => ({
        category,
        averageScore: Math.round(perf.totalScore / perf.totalQuizzes),
        totalQuizzes: perf.totalQuizzes
      }))
    }
  });
}));

/**
 * @route   GET /api/user/achievements
 * @desc    Get user achievements
 * @access  Private
 */
router.get('/achievements', authenticate, asyncHandler(async (req, res) => {
  const userId = req.user.id;
  
  const user = await User.findById(userId);
  const quizStats = await Quiz.getQuizStats(userId);
  
  const achievements = await calculateAchievements(user, quizStats[0]);

  res.json({
    success: true,
    data: {
      achievements
    }
  });
}));

/**
 * Helper function to calculate user achievements
 */
async function calculateAchievements(user, quizStats) {
  const achievements = [];

  // Quiz completion achievements
  if (quizStats?.totalQuizzes >= 1) {
    achievements.push({
      id: 'first_quiz',
      title: 'First Steps',
      description: 'Completed your first quiz',
      icon: '🎯',
      earned: true,
      earnedAt: user.createdAt
    });
  }

  if (quizStats?.totalQuizzes >= 10) {
    achievements.push({
      id: 'quiz_enthusiast',
      title: 'Quiz Enthusiast',
      description: 'Completed 10 quizzes',
      icon: '📚',
      earned: true
    });
  }

  if (quizStats?.totalQuizzes >= 50) {
    achievements.push({
      id: 'quiz_master',
      title: 'Quiz Master',
      description: 'Completed 50 quizzes',
      icon: '🏆',
      earned: true
    });
  }

  // Score achievements
  if (user.stats.bestScore >= 90) {
    achievements.push({
      id: 'high_scorer',
      title: 'High Scorer',
      description: 'Achieved 90%+ in a quiz',
      icon: '⭐',
      earned: true
    });
  }

  if (user.stats.bestScore === 100) {
    achievements.push({
      id: 'perfect_score',
      title: 'Perfect Score',
      description: 'Achieved 100% in a quiz',
      icon: '💯',
      earned: true
    });
  }

  // Streak achievements
  if (user.stats.streak.current >= 3) {
    achievements.push({
      id: 'streak_3',
      title: 'On Fire',
      description: '3-day quiz streak',
      icon: '🔥',
      earned: true
    });
  }

  if (user.stats.streak.longest >= 7) {
    achievements.push({
      id: 'streak_7',
      title: 'Week Warrior',
      description: '7-day quiz streak',
      icon: '⚡',
      earned: true
    });
  }

  return achievements;
}

/**
 * Helper function to generate fallback recommendations
 */
function generateFallbackRecommendations(categoryPerformance, weakAreas) {
  const recommendations = [];

  if (weakAreas.length > 0) {
    recommendations.push(
      `Focus on improving your ${weakAreas[0]} knowledge by taking more quizzes in this category.`,
      'Try starting with easier difficulty levels and gradually increase the challenge.',
      'Review explanations carefully after each question to understand the concepts better.'
    );
  } else {
    recommendations.push(
      'Great job! Try exploring new categories to broaden your knowledge.',
      'Challenge yourself with harder difficulty levels.',
      'Maintain your performance by taking regular quizzes.'
    );
  }

  return recommendations;
}

module.exports = router;
