<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Quiz Master - Test Your Knowledge</title>
    
    <!-- Meta Tags -->
    <meta name="description" content="Challenge yourself with AI-powered quizzes across multiple categories. Get instant feedback and track your progress.">
    <meta name="keywords" content="quiz, AI, learning, education, test, knowledge, ChatGPT, Gemini">
    <meta name="author" content="AI Quiz Master">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/favicon.ico">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/styles/main.css">
    <link rel="stylesheet" href="/styles/animations.css">
    
    <!-- Tailwind Config -->
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                    },
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        },
                        secondary: {
                            50: '#f8fafc',
                            100: '#f1f5f9',
                            200: '#e2e8f0',
                            300: '#cbd5e1',
                            400: '#94a3b8',
                            500: '#64748b',
                            600: '#475569',
                            700: '#334155',
                            800: '#1e293b',
                            900: '#0f172a',
                        }
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-up': 'slideUp 0.5s ease-out',
                        'slide-down': 'slideDown 0.5s ease-out',
                        'bounce-in': 'bounceIn 0.6s ease-out',
                        'pulse-slow': 'pulse 3s infinite',
                    }
                }
            }
        }
    </script>
</head>
<body class="font-inter bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
    <!-- Loading Screen -->
    <div id="loading-screen" class="fixed inset-0 bg-white dark:bg-gray-900 z-50 flex items-center justify-center">
        <div class="text-center">
            <div class="animate-spin rounded-full h-16 w-16 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <h2 class="text-xl font-semibold text-gray-800 dark:text-white">Loading AI Quiz Master...</h2>
            <p class="text-gray-600 dark:text-gray-400 mt-2">Preparing your learning experience</p>
        </div>
    </div>

    <!-- Navigation -->
    <nav id="navbar" class="bg-white dark:bg-gray-800 shadow-lg border-b border-gray-200 dark:border-gray-700 sticky top-0 z-40 transition-all duration-300">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="/" class="flex items-center space-x-2">
                        <div class="w-8 h-8 bg-gradient-to-r from-primary-500 to-purple-600 rounded-lg flex items-center justify-center">
                            <i class="fas fa-brain text-white text-sm"></i>
                        </div>
                        <span class="text-xl font-bold text-gray-800 dark:text-white">AI Quiz Master</span>
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="/" class="nav-link text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                        <i class="fas fa-home mr-2"></i>Home
                    </a>
                    <a href="/dashboard.html" class="nav-link text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                        <i class="fas fa-chart-line mr-2"></i>Dashboard
                    </a>
                    <a href="/leaderboard.html" class="nav-link text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                        <i class="fas fa-trophy mr-2"></i>Leaderboard
                    </a>
                </div>

                <!-- User Menu & Theme Toggle -->
                <div class="flex items-center space-x-4">
                    <!-- Theme Toggle -->
                    <button id="theme-toggle" class="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
                        <i class="fas fa-sun dark:hidden"></i>
                        <i class="fas fa-moon hidden dark:inline"></i>
                    </button>

                    <!-- User Menu -->
                    <div id="user-menu" class="relative">
                        <!-- Authenticated User -->
                        <div id="user-authenticated" class="hidden">
                            <button id="user-menu-button" class="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                                <img id="user-avatar" src="/assets/default-avatar.png" alt="User Avatar" class="w-8 h-8 rounded-full">
                                <span id="user-name" class="text-gray-700 dark:text-gray-300 font-medium"></span>
                                <i class="fas fa-chevron-down text-gray-500 text-sm"></i>
                            </button>
                            
                            <!-- Dropdown Menu -->
                            <div id="user-dropdown" class="hidden absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 py-2">
                                <a href="/dashboard.html" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                                    <i class="fas fa-chart-line mr-2"></i>Dashboard
                                </a>
                                <a href="/profile.html" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                                    <i class="fas fa-user mr-2"></i>Profile
                                </a>
                                <div id="admin-link" class="hidden">
                                    <a href="/admin.html" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                                        <i class="fas fa-cog mr-2"></i>Admin Panel
                                    </a>
                                </div>
                                <hr class="my-2 border-gray-200 dark:border-gray-700">
                                <button id="logout-btn" class="block w-full text-left px-4 py-2 text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                                    <i class="fas fa-sign-out-alt mr-2"></i>Logout
                                </button>
                            </div>
                        </div>

                        <!-- Guest User -->
                        <div id="user-guest" class="flex items-center space-x-2">
                            <a href="/login.html" class="px-4 py-2 text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium transition-colors">
                                Login
                            </a>
                            <a href="/register.html" class="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg font-medium transition-colors">
                                Sign Up
                            </a>
                        </div>
                    </div>

                    <!-- Mobile Menu Button -->
                    <button id="mobile-menu-button" class="md:hidden p-2 rounded-lg text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <div id="mobile-menu" class="hidden md:hidden bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
            <div class="px-4 py-2 space-y-2">
                <a href="/" class="block px-3 py-2 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                    <i class="fas fa-home mr-2"></i>Home
                </a>
                <a href="/dashboard.html" class="block px-3 py-2 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                    <i class="fas fa-chart-line mr-2"></i>Dashboard
                </a>
                <a href="/leaderboard.html" class="block px-3 py-2 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                    <i class="fas fa-trophy mr-2"></i>Leaderboard
                </a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main id="main-content" class="min-h-screen">
        <!-- Hero Section -->
        <section class="bg-gradient-to-br from-primary-600 via-purple-600 to-pink-600 text-white py-20">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                <div class="animate-fade-in">
                    <h1 class="text-4xl md:text-6xl font-bold mb-6">
                        Master Knowledge with
                        <span class="bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
                            AI-Powered Quizzes
                        </span>
                    </h1>
                    <p class="text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto">
                        Challenge yourself with intelligent quizzes powered by ChatGPT and Gemini AI. 
                        Get instant feedback, track progress, and compete with others.
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
                        <button id="start-quiz-btn" class="px-8 py-4 bg-white text-primary-600 rounded-lg font-semibold text-lg hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-lg">
                            <i class="fas fa-play mr-2"></i>Start Quiz Now
                        </button>
                        <a href="/leaderboard.html" class="px-8 py-4 border-2 border-white text-white rounded-lg font-semibold text-lg hover:bg-white hover:text-primary-600 transition-all duration-300">
                            <i class="fas fa-trophy mr-2"></i>View Leaderboard
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features Section -->
        <section class="py-20 bg-white dark:bg-gray-900">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-3xl md:text-4xl font-bold text-gray-800 dark:text-white mb-4">
                        Why Choose AI Quiz Master?
                    </h2>
                    <p class="text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                        Experience the future of learning with our AI-powered quiz platform
                    </p>
                </div>

                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- Feature 1 -->
                    <div class="feature-card bg-gray-50 dark:bg-gray-800 p-8 rounded-xl hover:shadow-lg transition-all duration-300 transform hover:-translate-y-2">
                        <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mb-6">
                            <i class="fas fa-robot text-white text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-800 dark:text-white mb-4">AI-Generated Questions</h3>
                        <p class="text-gray-600 dark:text-gray-400">
                            Dynamic questions created by ChatGPT ensure fresh, challenging content every time you play.
                        </p>
                    </div>

                    <!-- Feature 2 -->
                    <div class="feature-card bg-gray-50 dark:bg-gray-800 p-8 rounded-xl hover:shadow-lg transition-all duration-300 transform hover:-translate-y-2">
                        <div class="w-16 h-16 bg-gradient-to-r from-green-500 to-teal-600 rounded-lg flex items-center justify-center mb-6">
                            <i class="fas fa-comments text-white text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-800 dark:text-white mb-4">Smart Feedback</h3>
                        <p class="text-gray-600 dark:text-gray-400">
                            Get detailed explanations and personalized feedback powered by Google's Gemini AI.
                        </p>
                    </div>

                    <!-- Feature 3 -->
                    <div class="feature-card bg-gray-50 dark:bg-gray-800 p-8 rounded-xl hover:shadow-lg transition-all duration-300 transform hover:-translate-y-2">
                        <div class="w-16 h-16 bg-gradient-to-r from-orange-500 to-red-600 rounded-lg flex items-center justify-center mb-6">
                            <i class="fas fa-chart-line text-white text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-800 dark:text-white mb-4">Progress Tracking</h3>
                        <p class="text-gray-600 dark:text-gray-400">
                            Monitor your learning journey with detailed analytics and performance insights.
                        </p>
                    </div>

                    <!-- Feature 4 -->
                    <div class="feature-card bg-gray-50 dark:bg-gray-800 p-8 rounded-xl hover:shadow-lg transition-all duration-300 transform hover:-translate-y-2">
                        <div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg flex items-center justify-center mb-6">
                            <i class="fas fa-users text-white text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-800 dark:text-white mb-4">Competitive Learning</h3>
                        <p class="text-gray-600 dark:text-gray-400">
                            Compete with friends and learners worldwide on our global leaderboards.
                        </p>
                    </div>

                    <!-- Feature 5 -->
                    <div class="feature-card bg-gray-50 dark:bg-gray-800 p-8 rounded-xl hover:shadow-lg transition-all duration-300 transform hover:-translate-y-2">
                        <div class="w-16 h-16 bg-gradient-to-r from-indigo-500 to-blue-600 rounded-lg flex items-center justify-center mb-6">
                            <i class="fas fa-palette text-white text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-800 dark:text-white mb-4">Beautiful Design</h3>
                        <p class="text-gray-600 dark:text-gray-400">
                            Enjoy a modern, responsive interface with dark/light mode support.
                        </p>
                    </div>

                    <!-- Feature 6 -->
                    <div class="feature-card bg-gray-50 dark:bg-gray-800 p-8 rounded-xl hover:shadow-lg transition-all duration-300 transform hover:-translate-y-2">
                        <div class="w-16 h-16 bg-gradient-to-r from-yellow-500 to-orange-600 rounded-lg flex items-center justify-center mb-6">
                            <i class="fas fa-bolt text-white text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-800 dark:text-white mb-4">Real-time Experience</h3>
                        <p class="text-gray-600 dark:text-gray-400">
                            Instant scoring, live timers, and real-time feedback for an engaging experience.
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Categories Section -->
        <section id="categories-section" class="py-20 bg-gray-50 dark:bg-gray-800">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-3xl md:text-4xl font-bold text-gray-800 dark:text-white mb-4">
                        Explore Quiz Categories
                    </h2>
                    <p class="text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                        Choose from a wide range of topics to test and expand your knowledge
                    </p>
                </div>

                <div id="categories-grid" class="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    <!-- Categories will be loaded dynamically -->
                </div>
            </div>
        </section>

        <!-- CTA Section -->
        <section class="py-20 bg-gradient-to-r from-primary-600 to-purple-600 text-white">
            <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                <h2 class="text-3xl md:text-4xl font-bold mb-6">
                    Ready to Challenge Your Mind?
                </h2>
                <p class="text-xl mb-8 text-blue-100">
                    Join thousands of learners who are already improving their knowledge with AI Quiz Master
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <button id="cta-start-quiz" class="px-8 py-4 bg-white text-primary-600 rounded-lg font-semibold text-lg hover:bg-gray-100 transition-all duration-300 transform hover:scale-105">
                        <i class="fas fa-rocket mr-2"></i>Start Your First Quiz
                    </button>
                    <a href="/register.html" class="px-8 py-4 border-2 border-white text-white rounded-lg font-semibold text-lg hover:bg-white hover:text-primary-600 transition-all duration-300">
                        <i class="fas fa-user-plus mr-2"></i>Create Free Account
                    </a>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid md:grid-cols-4 gap-8">
                <div class="col-span-2">
                    <div class="flex items-center space-x-2 mb-4">
                        <div class="w-8 h-8 bg-gradient-to-r from-primary-500 to-purple-600 rounded-lg flex items-center justify-center">
                            <i class="fas fa-brain text-white text-sm"></i>
                        </div>
                        <span class="text-xl font-bold">AI Quiz Master</span>
                    </div>
                    <p class="text-gray-400 mb-4 max-w-md">
                        Revolutionizing learning through AI-powered quizzes. Challenge yourself, track progress, and compete with learners worldwide.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i class="fab fa-twitter text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i class="fab fa-facebook text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i class="fab fa-instagram text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i class="fab fa-linkedin text-xl"></i>
                        </a>
                    </div>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li><a href="/" class="text-gray-400 hover:text-white transition-colors">Home</a></li>
                        <li><a href="/dashboard.html" class="text-gray-400 hover:text-white transition-colors">Dashboard</a></li>
                        <li><a href="/leaderboard.html" class="text-gray-400 hover:text-white transition-colors">Leaderboard</a></li>
                        <li><a href="/about.html" class="text-gray-400 hover:text-white transition-colors">About</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">Support</h3>
                    <ul class="space-y-2">
                        <li><a href="/help.html" class="text-gray-400 hover:text-white transition-colors">Help Center</a></li>
                        <li><a href="/contact.html" class="text-gray-400 hover:text-white transition-colors">Contact Us</a></li>
                        <li><a href="/privacy.html" class="text-gray-400 hover:text-white transition-colors">Privacy Policy</a></li>
                        <li><a href="/terms.html" class="text-gray-400 hover:text-white transition-colors">Terms of Service</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="border-t border-gray-800 mt-8 pt-8 text-center">
                <p class="text-gray-400">
                    © 2024 AI Quiz Master. All rights reserved. Powered by ChatGPT & Gemini AI.
                </p>
            </div>
        </div>
    </footer>

    <!-- Modals -->
    <!-- Category Selection Modal -->
    <div id="category-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
        <div class="bg-white dark:bg-gray-800 rounded-xl max-w-2xl w-full max-h-[80vh] overflow-y-auto">
            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                <div class="flex justify-between items-center">
                    <h3 class="text-xl font-semibold text-gray-800 dark:text-white">Choose Quiz Category</h3>
                    <button id="close-category-modal" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            <div class="p-6">
                <div id="modal-categories-grid" class="grid md:grid-cols-2 gap-4">
                    <!-- Categories will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div id="toast-container" class="fixed top-4 right-4 z-50 space-y-2">
        <!-- Toasts will be added here dynamically -->
    </div>

    <!-- Scripts -->
    <script src="/js/main.js"></script>
    <script src="/js/auth.js"></script>
</body>
</html>
