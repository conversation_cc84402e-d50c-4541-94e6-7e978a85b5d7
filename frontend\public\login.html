<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - AI Quiz Master</title>
    
    <!-- Meta Tags -->
    <meta name="description" content="Login to AI Quiz Master and continue your learning journey with AI-powered quizzes.">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/favicon.ico">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/styles/main.css">
    <link rel="stylesheet" href="/styles/animations.css">
    
    <!-- Tailwind Config -->
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                    },
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="font-inter bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
    <!-- Loading Screen -->
    <div id="loading-screen" class="fixed inset-0 bg-white dark:bg-gray-900 z-50 flex items-center justify-center hidden">
        <div class="text-center">
            <div class="animate-spin rounded-full h-16 w-16 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <h2 class="text-xl font-semibold text-gray-800 dark:text-white">Signing you in...</h2>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="bg-white dark:bg-gray-800 shadow-lg border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="/" class="flex items-center space-x-2">
                        <div class="w-8 h-8 bg-gradient-to-r from-primary-500 to-purple-600 rounded-lg flex items-center justify-center">
                            <i class="fas fa-brain text-white text-sm"></i>
                        </div>
                        <span class="text-xl font-bold text-gray-800 dark:text-white">AI Quiz Master</span>
                    </a>
                </div>

                <!-- Theme Toggle -->
                <button id="theme-toggle" class="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
                    <i class="fas fa-sun dark:hidden"></i>
                    <i class="fas fa-moon hidden dark:inline"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <!-- Header -->
            <div class="text-center animate-fade-in">
                <div class="w-20 h-20 bg-gradient-to-r from-primary-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-sign-in-alt text-white text-2xl"></i>
                </div>
                <h2 class="text-3xl font-bold text-gray-900 dark:text-white">Welcome Back!</h2>
                <p class="mt-2 text-gray-600 dark:text-gray-400">Sign in to continue your learning journey</p>
            </div>

            <!-- Login Form -->
            <div class="card p-8 animate-slide-up">
                <!-- Form Errors -->
                <div id="form-errors" class="hidden mb-6"></div>

                <form id="login-form" class="space-y-6">
                    <!-- Email Field -->
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <i class="fas fa-envelope mr-2"></i>Email Address
                        </label>
                        <input 
                            id="email" 
                            name="email" 
                            type="email" 
                            autocomplete="email" 
                            required 
                            class="form-input"
                            placeholder="Enter your email address"
                        >
                    </div>

                    <!-- Password Field -->
                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <i class="fas fa-lock mr-2"></i>Password
                        </label>
                        <div class="relative">
                            <input 
                                id="password" 
                                name="password" 
                                type="password" 
                                autocomplete="current-password" 
                                required 
                                class="form-input pr-10"
                                placeholder="Enter your password"
                            >
                            <button 
                                type="button" 
                                id="toggle-password"
                                class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                            >
                                <i class="fas fa-eye" id="password-eye"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Remember Me & Forgot Password -->
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <input 
                                id="remember-me" 
                                name="remember-me" 
                                type="checkbox" 
                                class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                            >
                            <label for="remember-me" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                Remember me
                            </label>
                        </div>

                        <div class="text-sm">
                            <a href="/forgot-password.html" class="font-medium text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300">
                                Forgot your password?
                            </a>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div>
                        <button 
                            type="submit" 
                            class="btn btn-primary w-full text-lg py-3"
                            id="login-btn"
                        >
                            <i class="fas fa-sign-in-alt mr-2"></i>
                            Sign In
                        </button>
                    </div>
                </form>

                <!-- Divider -->
                <div class="mt-6">
                    <div class="relative">
                        <div class="absolute inset-0 flex items-center">
                            <div class="w-full border-t border-gray-300 dark:border-gray-600"></div>
                        </div>
                        <div class="relative flex justify-center text-sm">
                            <span class="px-2 bg-white dark:bg-gray-800 text-gray-500 dark:text-gray-400">
                                Don't have an account?
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Sign Up Link -->
                <div class="mt-6 text-center">
                    <a 
                        href="/register.html" 
                        class="font-medium text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300 transition-colors"
                    >
                        <i class="fas fa-user-plus mr-2"></i>
                        Create a new account
                    </a>
                </div>
            </div>

            <!-- Demo Credentials -->
            <div class="card p-6 bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 animate-bounce-in">
                <div class="text-center">
                    <h3 class="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-2">
                        <i class="fas fa-info-circle mr-2"></i>Demo Credentials
                    </h3>
                    <p class="text-blue-700 dark:text-blue-300 text-sm mb-3">
                        Try the app with these demo credentials:
                    </p>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between items-center bg-white dark:bg-gray-800 rounded px-3 py-2">
                            <span class="text-gray-600 dark:text-gray-400">Email:</span>
                            <code class="text-blue-600 dark:text-blue-400 font-mono"><EMAIL></code>
                        </div>
                        <div class="flex justify-between items-center bg-white dark:bg-gray-800 rounded px-3 py-2">
                            <span class="text-gray-600 dark:text-gray-400">Password:</span>
                            <code class="text-blue-600 dark:text-blue-400 font-mono">Demo123!</code>
                        </div>
                    </div>
                    <button 
                        id="use-demo-btn"
                        class="mt-3 btn btn-secondary text-sm"
                    >
                        <i class="fas fa-magic mr-2"></i>Use Demo Credentials
                    </button>
                </div>
            </div>

            <!-- Back to Home -->
            <div class="text-center">
                <a 
                    href="/" 
                    class="inline-flex items-center text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
                >
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Home
                </a>
            </div>
        </div>
    </main>

    <!-- Toast Notifications -->
    <div id="toast-container" class="fixed top-4 right-4 z-50 space-y-2">
        <!-- Toasts will be added here dynamically -->
    </div>

    <!-- Scripts -->
    <script src="/js/main.js"></script>
    <script src="/js/auth.js"></script>
    
    <!-- Login Page Specific Scripts -->
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // Password toggle functionality
            const togglePassword = document.getElementById('toggle-password');
            const passwordInput = document.getElementById('password');
            const passwordEye = document.getElementById('password-eye');

            if (togglePassword && passwordInput && passwordEye) {
                togglePassword.addEventListener('click', () => {
                    const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                    passwordInput.setAttribute('type', type);
                    
                    if (type === 'password') {
                        passwordEye.classList.remove('fa-eye-slash');
                        passwordEye.classList.add('fa-eye');
                    } else {
                        passwordEye.classList.remove('fa-eye');
                        passwordEye.classList.add('fa-eye-slash');
                    }
                });
            }

            // Demo credentials button
            const useDemoBtn = document.getElementById('use-demo-btn');
            if (useDemoBtn) {
                useDemoBtn.addEventListener('click', () => {
                    document.getElementById('email').value = '<EMAIL>';
                    document.getElementById('password').value = 'Demo123!';
                    Toast.info('Demo credentials filled in. Click Sign In to continue.');
                });
            }

            // Auto-focus email field
            const emailInput = document.getElementById('email');
            if (emailInput) {
                emailInput.focus();
            }

            // Check if user is already logged in
            if (AppState.user) {
                Toast.info('You are already logged in. Redirecting...');
                setTimeout(() => {
                    window.location.href = '/dashboard.html';
                }, 1500);
            }
        });
    </script>
</body>
</html>
