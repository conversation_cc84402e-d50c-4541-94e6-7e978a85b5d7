const express = require('express');
const User = require('../models/User');
const Quiz = require('../models/Quiz');
const Question = require('../models/Question');
const Category = require('../models/Category');
const { authenticate, authorize } = require('../middleware/auth');
const { validateCategoryCreation, validateQuestionCreation, validatePagination, validateObjectId } = require('../middleware/validation');
const { asyncHandler } = require('../middleware/errorHandler');
const chatgptService = require('../services/chatgptService');

const router = express.Router();

// All admin routes require authentication and admin role
router.use(authenticate);
router.use(authorize('admin'));

/**
 * @route   GET /api/admin/dashboard
 * @desc    Get admin dashboard statistics
 * @access  Private (Admin only)
 */
router.get('/dashboard', asyncHandler(async (req, res) => {
  // Get overall statistics
  const totalUsers = await User.countDocuments({ isActive: true });
  const totalQuizzes = await Quiz.countDocuments();
  const totalQuestions = await Question.countDocuments({ isActive: true });
  const totalCategories = await Category.countDocuments({ isActive: true });

  // Get recent activity
  const recentUsers = await User.find({ isActive: true })
    .select('username firstName lastName createdAt lastLogin')
    .sort({ createdAt: -1 })
    .limit(5);

  const recentQuizzes = await Quiz.find({ status: 'completed' })
    .populate('user', 'username firstName lastName')
    .populate('category', 'name')
    .select('user category results completedAt')
    .sort({ completedAt: -1 })
    .limit(10);

  // Get category statistics
  const categoryStats = await Category.aggregate([
    { $match: { isActive: true } },
    {
      $project: {
        name: 1,
        totalQuestions: 1,
        totalQuizzes: 1,
        averageScore: 1
      }
    },
    { $sort: { totalQuizzes: -1 } }
  ]);

  // Get user activity trends (last 30 days)
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

  const userTrends = await User.aggregate([
    {
      $match: {
        createdAt: { $gte: thirtyDaysAgo }
      }
    },
    {
      $group: {
        _id: {
          $dateToString: { format: '%Y-%m-%d', date: '$createdAt' }
        },
        newUsers: { $sum: 1 }
      }
    },
    { $sort: { '_id': 1 } }
  ]);

  const quizTrends = await Quiz.aggregate([
    {
      $match: {
        completedAt: { $gte: thirtyDaysAgo },
        status: 'completed'
      }
    },
    {
      $group: {
        _id: {
          $dateToString: { format: '%Y-%m-%d', date: '$completedAt' }
        },
        completedQuizzes: { $sum: 1 }
      }
    },
    { $sort: { '_id': 1 } }
  ]);

  res.json({
    success: true,
    data: {
      overview: {
        totalUsers,
        totalQuizzes,
        totalQuestions,
        totalCategories
      },
      recentActivity: {
        users: recentUsers,
        quizzes: recentQuizzes
      },
      categoryStats,
      trends: {
        users: userTrends,
        quizzes: quizTrends
      }
    }
  });
}));

/**
 * @route   GET /api/admin/users
 * @desc    Get all users with pagination
 * @access  Private (Admin only)
 */
router.get('/users', validatePagination, asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 20;
  const search = req.query.search || '';
  const skip = (page - 1) * limit;

  // Build search query
  const searchQuery = search ? {
    $or: [
      { username: { $regex: search, $options: 'i' } },
      { email: { $regex: search, $options: 'i' } },
      { firstName: { $regex: search, $options: 'i' } },
      { lastName: { $regex: search, $options: 'i' } }
    ]
  } : {};

  const users = await User.find(searchQuery)
    .select('-password')
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit);

  const totalUsers = await User.countDocuments(searchQuery);

  res.json({
    success: true,
    data: {
      users,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(totalUsers / limit),
        totalUsers,
        hasNext: page < Math.ceil(totalUsers / limit),
        hasPrev: page > 1
      }
    }
  });
}));

/**
 * @route   PUT /api/admin/users/:userId/status
 * @desc    Update user status (activate/deactivate)
 * @access  Private (Admin only)
 */
router.put('/users/:userId/status', validateObjectId('userId'), asyncHandler(async (req, res) => {
  const { userId } = req.params;
  const { isActive } = req.body;

  const user = await User.findById(userId);
  if (!user) {
    return res.status(404).json({
      success: false,
      message: 'User not found'
    });
  }

  user.isActive = isActive;
  await user.save();

  res.json({
    success: true,
    message: `User ${isActive ? 'activated' : 'deactivated'} successfully`,
    data: { user }
  });
}));

/**
 * @route   GET /api/admin/categories
 * @desc    Get all categories for admin
 * @access  Private (Admin only)
 */
router.get('/categories', asyncHandler(async (req, res) => {
  const categories = await Category.find()
    .populate('createdBy', 'username firstName lastName')
    .sort({ createdAt: -1 });

  res.json({
    success: true,
    data: {
      categories,
      count: categories.length
    }
  });
}));

/**
 * @route   POST /api/admin/categories
 * @desc    Create new category
 * @access  Private (Admin only)
 */
router.post('/categories', validateCategoryCreation, asyncHandler(async (req, res) => {
  const { name, description, icon, color } = req.body;

  const category = new Category({
    name,
    description,
    icon: icon || 'fas fa-question-circle',
    color: color || '#3B82F6',
    createdBy: req.user.id
  });

  await category.save();

  res.status(201).json({
    success: true,
    message: 'Category created successfully',
    data: { category }
  });
}));

/**
 * @route   PUT /api/admin/categories/:categoryId
 * @desc    Update category
 * @access  Private (Admin only)
 */
router.put('/categories/:categoryId', 
  validateObjectId('categoryId'), 
  validateCategoryCreation, 
  asyncHandler(async (req, res) => {
    const { categoryId } = req.params;
    const { name, description, icon, color, isActive } = req.body;

    const category = await Category.findById(categoryId);
    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'Category not found'
      });
    }

    if (name) category.name = name;
    if (description) category.description = description;
    if (icon) category.icon = icon;
    if (color) category.color = color;
    if (typeof isActive === 'boolean') category.isActive = isActive;

    await category.save();

    res.json({
      success: true,
      message: 'Category updated successfully',
      data: { category }
    });
  })
);

/**
 * @route   DELETE /api/admin/categories/:categoryId
 * @desc    Delete category (soft delete)
 * @access  Private (Admin only)
 */
router.delete('/categories/:categoryId', validateObjectId('categoryId'), asyncHandler(async (req, res) => {
  const { categoryId } = req.params;

  const category = await Category.findById(categoryId);
  if (!category) {
    return res.status(404).json({
      success: false,
      message: 'Category not found'
    });
  }

  // Check if category has questions
  const questionCount = await Question.countDocuments({ category: categoryId, isActive: true });
  if (questionCount > 0) {
    return res.status(400).json({
      success: false,
      message: 'Cannot delete category with active questions. Deactivate it instead.'
    });
  }

  category.isActive = false;
  await category.save();

  res.json({
    success: true,
    message: 'Category deactivated successfully'
  });
}));

/**
 * @route   GET /api/admin/questions
 * @desc    Get all questions with pagination
 * @access  Private (Admin only)
 */
router.get('/questions', validatePagination, asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 20;
  const category = req.query.category;
  const difficulty = req.query.difficulty;
  const skip = (page - 1) * limit;

  // Build filter query
  const filterQuery = { isActive: true };
  if (category) filterQuery.category = category;
  if (difficulty) filterQuery.difficulty = difficulty;

  const questions = await Question.find(filterQuery)
    .populate('category', 'name slug')
    .populate('createdBy', 'username firstName lastName')
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit);

  const totalQuestions = await Question.countDocuments(filterQuery);

  res.json({
    success: true,
    data: {
      questions,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(totalQuestions / limit),
        totalQuestions,
        hasNext: page < Math.ceil(totalQuestions / limit),
        hasPrev: page > 1
      }
    }
  });
}));

/**
 * @route   POST /api/admin/questions
 * @desc    Create new question
 * @access  Private (Admin only)
 */
router.post('/questions', validateQuestionCreation, asyncHandler(async (req, res) => {
  const questionData = {
    ...req.body,
    createdBy: req.user.id,
    source: 'manual'
  };

  const question = new Question(questionData);
  await question.save();

  await question.populate('category', 'name slug');

  res.status(201).json({
    success: true,
    message: 'Question created successfully',
    data: { question }
  });
}));

/**
 * @route   POST /api/admin/questions/generate
 * @desc    Generate questions using AI
 * @access  Private (Admin only)
 */
router.post('/questions/generate', asyncHandler(async (req, res) => {
  const { category, difficulty, count, customPrompt } = req.body;

  if (!chatgptService.isAvailable()) {
    return res.status(503).json({
      success: false,
      message: 'AI question generation service is not available'
    });
  }

  try {
    // Verify category exists
    const categoryDoc = await Category.findById(category);
    if (!categoryDoc) {
      return res.status(404).json({
        success: false,
        message: 'Category not found'
      });
    }

    // Generate questions using ChatGPT
    const aiQuestions = await chatgptService.generateQuestions(
      categoryDoc.name,
      difficulty || 'medium',
      count || 5,
      customPrompt
    );

    // Save generated questions to database
    const savedQuestions = await Promise.all(
      aiQuestions.map(async (q) => {
        const question = new Question({
          ...q,
          category: categoryDoc._id,
          createdBy: req.user.id,
          source: 'chatgpt',
          aiPrompt: customPrompt || `Generate ${difficulty || 'medium'} ${categoryDoc.name} questions`
        });
        await question.save();
        return question.populate('category', 'name slug');
      })
    );

    res.status(201).json({
      success: true,
      message: `${savedQuestions.length} questions generated successfully`,
      data: {
        questions: savedQuestions,
        count: savedQuestions.length
      }
    });
  } catch (error) {
    console.error('AI question generation error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate questions using AI',
      error: error.message
    });
  }
}));

module.exports = router;
