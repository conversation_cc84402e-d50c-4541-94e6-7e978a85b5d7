/**
 * AI Quiz Master - Main JavaScript
 * Handles core functionality, UI interactions, and API calls
 */

// Global Configuration
const CONFIG = {
    API_BASE_URL: window.location.hostname === 'localhost' ? 'http://localhost:5000/api' : '/api',
    STORAGE_KEYS: {
        TOKEN: 'quiz_token',
        USER: 'quiz_user',
        THEME: 'quiz_theme',
        PREFERENCES: 'quiz_preferences'
    },
    THEMES: {
        LIGHT: 'light',
        DARK: 'dark'
    }
};

// Global State
const AppState = {
    user: null,
    token: null,
    theme: 'light',
    categories: [],
    currentQuiz: null,
    isLoading: false
};

// Utility Functions
const Utils = {
    // Local Storage helpers
    setStorage(key, value) {
        try {
            localStorage.setItem(key, JSON.stringify(value));
        } catch (error) {
            console.error('Error saving to localStorage:', error);
        }
    },

    getStorage(key) {
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : null;
        } catch (error) {
            console.error('Error reading from localStorage:', error);
            return null;
        }
    },

    removeStorage(key) {
        try {
            localStorage.removeItem(key);
        } catch (error) {
            console.error('Error removing from localStorage:', error);
        }
    },

    // API helpers
    async apiCall(endpoint, options = {}) {
        const url = `${CONFIG.API_BASE_URL}${endpoint}`;
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
            }
        };

        // Add auth token if available
        if (AppState.token) {
            defaultOptions.headers.Authorization = `Bearer ${AppState.token}`;
        }

        const finalOptions = {
            ...defaultOptions,
            ...options,
            headers: {
                ...defaultOptions.headers,
                ...options.headers
            }
        };

        try {
            const response = await fetch(url, finalOptions);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || `HTTP error! status: ${response.status}`);
            }

            return data;
        } catch (error) {
            console.error('API call failed:', error);
            throw error;
        }
    },

    // Format time in MM:SS format
    formatTime(seconds) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    },

    // Format date
    formatDate(date) {
        return new Date(date).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    },

    // Debounce function
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // Generate random ID
    generateId() {
        return Math.random().toString(36).substr(2, 9);
    }
};

// Toast Notification System
const Toast = {
    container: null,

    init() {
        this.container = document.getElementById('toast-container');
        if (!this.container) {
            this.container = document.createElement('div');
            this.container.id = 'toast-container';
            this.container.className = 'fixed top-4 right-4 z-50 space-y-2';
            document.body.appendChild(this.container);
        }
    },

    show(message, type = 'info', duration = 5000) {
        if (!this.container) this.init();

        const toast = document.createElement('div');
        const id = Utils.generateId();
        
        toast.id = `toast-${id}`;
        toast.className = `toast ${type}`;
        toast.innerHTML = `
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <i class="fas ${this.getIcon(type)} mr-2"></i>
                    <span>${message}</span>
                </div>
                <button onclick="Toast.hide('${id}')" class="ml-4 text-white hover:text-gray-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        this.container.appendChild(toast);

        // Trigger animation
        setTimeout(() => toast.classList.add('show'), 100);

        // Auto remove
        if (duration > 0) {
            setTimeout(() => this.hide(id), duration);
        }

        return id;
    },

    hide(id) {
        const toast = document.getElementById(`toast-${id}`);
        if (toast) {
            toast.classList.remove('show');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }
    },

    getIcon(type) {
        const icons = {
            success: 'fa-check-circle',
            error: 'fa-exclamation-circle',
            warning: 'fa-exclamation-triangle',
            info: 'fa-info-circle'
        };
        return icons[type] || icons.info;
    },

    success(message, duration) {
        return this.show(message, 'success', duration);
    },

    error(message, duration) {
        return this.show(message, 'error', duration);
    },

    warning(message, duration) {
        return this.show(message, 'warning', duration);
    },

    info(message, duration) {
        return this.show(message, 'info', duration);
    }
};

// Theme Management
const ThemeManager = {
    init() {
        const savedTheme = Utils.getStorage(CONFIG.STORAGE_KEYS.THEME) || CONFIG.THEMES.LIGHT;
        this.setTheme(savedTheme);
        this.bindEvents();
    },

    setTheme(theme) {
        AppState.theme = theme;
        document.documentElement.classList.toggle('dark', theme === CONFIG.THEMES.DARK);
        Utils.setStorage(CONFIG.STORAGE_KEYS.THEME, theme);
        
        // Update theme toggle button
        const themeToggle = document.getElementById('theme-toggle');
        if (themeToggle) {
            const sunIcon = themeToggle.querySelector('.fa-sun');
            const moonIcon = themeToggle.querySelector('.fa-moon');
            
            if (theme === CONFIG.THEMES.DARK) {
                sunIcon?.classList.add('hidden');
                moonIcon?.classList.remove('hidden');
            } else {
                sunIcon?.classList.remove('hidden');
                moonIcon?.classList.add('hidden');
            }
        }
    },

    toggle() {
        const newTheme = AppState.theme === CONFIG.THEMES.LIGHT ? CONFIG.THEMES.DARK : CONFIG.THEMES.LIGHT;
        this.setTheme(newTheme);
    },

    bindEvents() {
        const themeToggle = document.getElementById('theme-toggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => this.toggle());
        }
    }
};

// Loading Manager
const LoadingManager = {
    show(message = 'Loading...') {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            const messageEl = loadingScreen.querySelector('h2');
            if (messageEl) {
                messageEl.textContent = message;
            }
            loadingScreen.classList.remove('hidden');
        }
        AppState.isLoading = true;
    },

    hide() {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            loadingScreen.classList.add('hidden');
        }
        AppState.isLoading = false;
    }
};

// Navigation Manager
const NavigationManager = {
    init() {
        this.bindEvents();
        this.updateNavigation();
    },

    bindEvents() {
        // Mobile menu toggle
        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const mobileMenu = document.getElementById('mobile-menu');
        
        if (mobileMenuButton && mobileMenu) {
            mobileMenuButton.addEventListener('click', () => {
                mobileMenu.classList.toggle('hidden');
            });
        }

        // User menu toggle
        const userMenuButton = document.getElementById('user-menu-button');
        const userDropdown = document.getElementById('user-dropdown');
        
        if (userMenuButton && userDropdown) {
            userMenuButton.addEventListener('click', (e) => {
                e.stopPropagation();
                userDropdown.classList.toggle('hidden');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', () => {
                userDropdown.classList.add('hidden');
            });
        }

        // Logout button
        const logoutBtn = document.getElementById('logout-btn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => this.logout());
        }
    },

    updateNavigation() {
        const userAuthenticated = document.getElementById('user-authenticated');
        const userGuest = document.getElementById('user-guest');
        const adminLink = document.getElementById('admin-link');

        if (AppState.user) {
            // Show authenticated user menu
            if (userAuthenticated) userAuthenticated.classList.remove('hidden');
            if (userGuest) userGuest.classList.add('hidden');

            // Update user info
            const userName = document.getElementById('user-name');
            const userAvatar = document.getElementById('user-avatar');
            
            if (userName) userName.textContent = AppState.user.username;
            if (userAvatar) userAvatar.src = AppState.user.avatar || '/assets/default-avatar.png';

            // Show admin link if user is admin
            if (adminLink && AppState.user.role === 'admin') {
                adminLink.classList.remove('hidden');
            }
        } else {
            // Show guest menu
            if (userAuthenticated) userAuthenticated.classList.add('hidden');
            if (userGuest) userGuest.classList.remove('hidden');
            if (adminLink) adminLink.classList.add('hidden');
        }
    },

    async logout() {
        try {
            // Call logout API
            await Utils.apiCall('/auth/logout', { method: 'POST' });
        } catch (error) {
            console.error('Logout API call failed:', error);
        } finally {
            // Clear local storage and state
            Utils.removeStorage(CONFIG.STORAGE_KEYS.TOKEN);
            Utils.removeStorage(CONFIG.STORAGE_KEYS.USER);
            AppState.user = null;
            AppState.token = null;
            
            // Update navigation
            this.updateNavigation();
            
            // Show success message
            Toast.success('Logged out successfully');
            
            // Redirect to home page
            setTimeout(() => {
                window.location.href = '/';
            }, 1000);
        }
    }
};

// Categories Manager
const CategoriesManager = {
    async loadCategories() {
        try {
            const response = await Utils.apiCall('/quiz/categories');
            AppState.categories = response.data.categories;
            this.renderCategories();
        } catch (error) {
            console.error('Failed to load categories:', error);
            Toast.error('Failed to load quiz categories');
        }
    },

    renderCategories() {
        const categoriesGrid = document.getElementById('categories-grid');
        const modalCategoriesGrid = document.getElementById('modal-categories-grid');
        
        if (!AppState.categories.length) return;

        const categoryHTML = AppState.categories.map(category => `
            <div class="category-card" data-category-id="${category._id}" onclick="CategoriesManager.selectCategory('${category._id}')">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 rounded-lg flex items-center justify-center text-white text-xl" style="background-color: ${category.color}">
                        <i class="${category.icon}"></i>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white">${category.name}</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">${category.totalQuestions} questions</p>
                    </div>
                </div>
                <p class="text-gray-600 dark:text-gray-400 text-sm mb-4">${category.description}</p>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-500 dark:text-gray-400">${category.totalQuizzes} quizzes taken</span>
                    <div class="flex items-center text-yellow-500">
                        <i class="fas fa-star text-sm"></i>
                        <span class="ml-1 text-sm">${category.averageScore ? category.averageScore.toFixed(1) : 'N/A'}</span>
                    </div>
                </div>
            </div>
        `).join('');

        if (categoriesGrid) categoriesGrid.innerHTML = categoryHTML;
        if (modalCategoriesGrid) modalCategoriesGrid.innerHTML = categoryHTML;
    },

    selectCategory(categoryId) {
        const category = AppState.categories.find(c => c._id === categoryId);
        if (!category) return;

        if (!AppState.user) {
            Toast.warning('Please login to start a quiz');
            setTimeout(() => {
                window.location.href = '/login.html';
            }, 1500);
            return;
        }

        // Store selected category and redirect to quiz setup
        Utils.setStorage('selectedCategory', category);
        window.location.href = '/quiz.html';
    }
};

// App Initialization
const App = {
    async init() {
        try {
            // Show loading screen
            LoadingManager.show('Initializing AI Quiz Master...');

            // Initialize theme
            ThemeManager.init();

            // Load user data from storage
            AppState.token = Utils.getStorage(CONFIG.STORAGE_KEYS.TOKEN);
            AppState.user = Utils.getStorage(CONFIG.STORAGE_KEYS.USER);

            // Verify token if exists
            if (AppState.token) {
                try {
                    const response = await Utils.apiCall('/auth/me');
                    AppState.user = response.data.user;
                    Utils.setStorage(CONFIG.STORAGE_KEYS.USER, AppState.user);
                } catch (error) {
                    console.error('Token verification failed:', error);
                    // Clear invalid token
                    Utils.removeStorage(CONFIG.STORAGE_KEYS.TOKEN);
                    Utils.removeStorage(CONFIG.STORAGE_KEYS.USER);
                    AppState.token = null;
                    AppState.user = null;
                }
            }

            // Initialize components
            NavigationManager.init();
            Toast.init();

            // Load categories
            await CategoriesManager.loadCategories();

            // Bind global events
            this.bindEvents();

            // Hide loading screen
            LoadingManager.hide();

            // Show welcome message for new users
            if (AppState.user && !Utils.getStorage('welcomed')) {
                setTimeout(() => {
                    Toast.success(`Welcome back, ${AppState.user.firstName}!`);
                    Utils.setStorage('welcomed', true);
                }, 1000);
            }

        } catch (error) {
            console.error('App initialization failed:', error);
            LoadingManager.hide();
            Toast.error('Failed to initialize the application');
        }
    },

    bindEvents() {
        // Start quiz buttons
        const startQuizBtns = document.querySelectorAll('#start-quiz-btn, #cta-start-quiz');
        startQuizBtns.forEach(btn => {
            btn.addEventListener('click', () => this.showCategoryModal());
        });

        // Category modal events
        const categoryModal = document.getElementById('category-modal');
        const closeCategoryModal = document.getElementById('close-category-modal');
        
        if (closeCategoryModal) {
            closeCategoryModal.addEventListener('click', () => this.hideCategoryModal());
        }

        if (categoryModal) {
            categoryModal.addEventListener('click', (e) => {
                if (e.target === categoryModal) {
                    this.hideCategoryModal();
                }
            });
        }

        // Handle escape key for modals
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.hideCategoryModal();
            }
        });
    },

    showCategoryModal() {
        if (!AppState.user) {
            Toast.warning('Please login to start a quiz');
            setTimeout(() => {
                window.location.href = '/login.html';
            }, 1500);
            return;
        }

        const modal = document.getElementById('category-modal');
        if (modal) {
            modal.classList.remove('hidden');
            modal.classList.add('flex');
        }
    },

    hideCategoryModal() {
        const modal = document.getElementById('category-modal');
        if (modal) {
            modal.classList.add('hidden');
            modal.classList.remove('flex');
        }
    }
};

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    App.init();
});

// Export for global access
window.App = App;
window.Utils = Utils;
window.Toast = Toast;
window.AppState = AppState;
window.CategoriesManager = CategoriesManager;
