<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Up - AI Quiz Master</title>
    
    <!-- Meta Tags -->
    <meta name="description" content="Create your AI Quiz Master account and start your personalized learning journey.">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/favicon.ico">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/styles/main.css">
    <link rel="stylesheet" href="/styles/animations.css">
    
    <!-- Tailwind Config -->
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                    },
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="font-inter bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
    <!-- Loading Screen -->
    <div id="loading-screen" class="fixed inset-0 bg-white dark:bg-gray-900 z-50 flex items-center justify-center hidden">
        <div class="text-center">
            <div class="animate-spin rounded-full h-16 w-16 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <h2 class="text-xl font-semibold text-gray-800 dark:text-white">Creating your account...</h2>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="bg-white dark:bg-gray-800 shadow-lg border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="/" class="flex items-center space-x-2">
                        <div class="w-8 h-8 bg-gradient-to-r from-primary-500 to-purple-600 rounded-lg flex items-center justify-center">
                            <i class="fas fa-brain text-white text-sm"></i>
                        </div>
                        <span class="text-xl font-bold text-gray-800 dark:text-white">AI Quiz Master</span>
                    </a>
                </div>

                <!-- Theme Toggle -->
                <button id="theme-toggle" class="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
                    <i class="fas fa-sun dark:hidden"></i>
                    <i class="fas fa-moon hidden dark:inline"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <!-- Header -->
            <div class="text-center animate-fade-in">
                <div class="w-20 h-20 bg-gradient-to-r from-primary-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-user-plus text-white text-2xl"></i>
                </div>
                <h2 class="text-3xl font-bold text-gray-900 dark:text-white">Join AI Quiz Master</h2>
                <p class="mt-2 text-gray-600 dark:text-gray-400">Create your account and start learning with AI</p>
            </div>

            <!-- Registration Form -->
            <div class="card p-8 animate-slide-up">
                <!-- Form Errors -->
                <div id="form-errors" class="hidden mb-6"></div>

                <form id="register-form" class="space-y-6">
                    <!-- Name Fields -->
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label for="firstName" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                <i class="fas fa-user mr-2"></i>First Name
                            </label>
                            <input 
                                id="firstName" 
                                name="firstName" 
                                type="text" 
                                autocomplete="given-name" 
                                required 
                                class="form-input"
                                placeholder="John"
                            >
                        </div>
                        <div>
                            <label for="lastName" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Last Name
                            </label>
                            <input 
                                id="lastName" 
                                name="lastName" 
                                type="text" 
                                autocomplete="family-name" 
                                required 
                                class="form-input"
                                placeholder="Doe"
                            >
                        </div>
                    </div>

                    <!-- Username Field -->
                    <div>
                        <label for="username" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <i class="fas fa-at mr-2"></i>Username
                        </label>
                        <input 
                            id="username" 
                            name="username" 
                            type="text" 
                            autocomplete="username" 
                            required 
                            class="form-input"
                            placeholder="johndoe123"
                        >
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                            Only letters, numbers, and underscores allowed. Minimum 3 characters.
                        </p>
                    </div>

                    <!-- Email Field -->
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <i class="fas fa-envelope mr-2"></i>Email Address
                        </label>
                        <input 
                            id="email" 
                            name="email" 
                            type="email" 
                            autocomplete="email" 
                            required 
                            class="form-input"
                            placeholder="<EMAIL>"
                        >
                    </div>

                    <!-- Password Field -->
                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <i class="fas fa-lock mr-2"></i>Password
                        </label>
                        <div class="relative">
                            <input 
                                id="password" 
                                name="password" 
                                type="password" 
                                autocomplete="new-password" 
                                required 
                                class="form-input pr-10"
                                placeholder="Create a strong password"
                            >
                            <button 
                                type="button" 
                                id="toggle-password"
                                class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                            >
                                <i class="fas fa-eye" id="password-eye"></i>
                            </button>
                        </div>
                        <div class="mt-2">
                            <div class="text-xs text-gray-500 dark:text-gray-400 space-y-1">
                                <div class="flex items-center">
                                    <i class="fas fa-check text-green-500 w-3 mr-2 hidden" id="length-check"></i>
                                    <i class="fas fa-times text-red-500 w-3 mr-2" id="length-cross"></i>
                                    <span>At least 6 characters</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-check text-green-500 w-3 mr-2 hidden" id="case-check"></i>
                                    <i class="fas fa-times text-red-500 w-3 mr-2" id="case-cross"></i>
                                    <span>Upper & lowercase letters</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-check text-green-500 w-3 mr-2 hidden" id="number-check"></i>
                                    <i class="fas fa-times text-red-500 w-3 mr-2" id="number-cross"></i>
                                    <span>At least one number</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Confirm Password Field -->
                    <div>
                        <label for="confirmPassword" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <i class="fas fa-lock mr-2"></i>Confirm Password
                        </label>
                        <div class="relative">
                            <input 
                                id="confirmPassword" 
                                name="confirmPassword" 
                                type="password" 
                                autocomplete="new-password" 
                                required 
                                class="form-input pr-10"
                                placeholder="Confirm your password"
                            >
                            <button 
                                type="button" 
                                id="toggle-confirm-password"
                                class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                            >
                                <i class="fas fa-eye" id="confirm-password-eye"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Terms and Privacy -->
                    <div class="flex items-start">
                        <input 
                            id="terms" 
                            name="terms" 
                            type="checkbox" 
                            required
                            class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded mt-1"
                        >
                        <label for="terms" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                            I agree to the 
                            <a href="/terms.html" class="text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300">Terms of Service</a>
                            and 
                            <a href="/privacy.html" class="text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300">Privacy Policy</a>
                        </label>
                    </div>

                    <!-- Submit Button -->
                    <div>
                        <button 
                            type="submit" 
                            class="btn btn-primary w-full text-lg py-3"
                            id="register-btn"
                        >
                            <i class="fas fa-user-plus mr-2"></i>
                            Create Account
                        </button>
                    </div>
                </form>

                <!-- Divider -->
                <div class="mt-6">
                    <div class="relative">
                        <div class="absolute inset-0 flex items-center">
                            <div class="w-full border-t border-gray-300 dark:border-gray-600"></div>
                        </div>
                        <div class="relative flex justify-center text-sm">
                            <span class="px-2 bg-white dark:bg-gray-800 text-gray-500 dark:text-gray-400">
                                Already have an account?
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Sign In Link -->
                <div class="mt-6 text-center">
                    <a 
                        href="/login.html" 
                        class="font-medium text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300 transition-colors"
                    >
                        <i class="fas fa-sign-in-alt mr-2"></i>
                        Sign in to your account
                    </a>
                </div>
            </div>

            <!-- Benefits -->
            <div class="card p-6 bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 animate-bounce-in">
                <div class="text-center">
                    <h3 class="text-lg font-semibold text-green-800 dark:text-green-200 mb-3">
                        <i class="fas fa-gift mr-2"></i>What You Get
                    </h3>
                    <div class="space-y-2 text-sm text-green-700 dark:text-green-300">
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-green-500 mr-3"></i>
                            <span>Unlimited AI-powered quizzes</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-green-500 mr-3"></i>
                            <span>Personalized learning insights</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-green-500 mr-3"></i>
                            <span>Progress tracking & analytics</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-green-500 mr-3"></i>
                            <span>Global leaderboards</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-green-500 mr-3"></i>
                            <span>Smart AI feedback</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Back to Home -->
            <div class="text-center">
                <a 
                    href="/" 
                    class="inline-flex items-center text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
                >
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Home
                </a>
            </div>
        </div>
    </main>

    <!-- Toast Notifications -->
    <div id="toast-container" class="fixed top-4 right-4 z-50 space-y-2">
        <!-- Toasts will be added here dynamically -->
    </div>

    <!-- Scripts -->
    <script src="/js/main.js"></script>
    <script src="/js/auth.js"></script>
    
    <!-- Registration Page Specific Scripts -->
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // Password toggle functionality
            const togglePassword = document.getElementById('toggle-password');
            const passwordInput = document.getElementById('password');
            const passwordEye = document.getElementById('password-eye');

            if (togglePassword && passwordInput && passwordEye) {
                togglePassword.addEventListener('click', () => {
                    const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                    passwordInput.setAttribute('type', type);
                    
                    if (type === 'password') {
                        passwordEye.classList.remove('fa-eye-slash');
                        passwordEye.classList.add('fa-eye');
                    } else {
                        passwordEye.classList.remove('fa-eye');
                        passwordEye.classList.add('fa-eye-slash');
                    }
                });
            }

            // Confirm password toggle
            const toggleConfirmPassword = document.getElementById('toggle-confirm-password');
            const confirmPasswordInput = document.getElementById('confirmPassword');
            const confirmPasswordEye = document.getElementById('confirm-password-eye');

            if (toggleConfirmPassword && confirmPasswordInput && confirmPasswordEye) {
                toggleConfirmPassword.addEventListener('click', () => {
                    const type = confirmPasswordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                    confirmPasswordInput.setAttribute('type', type);
                    
                    if (type === 'password') {
                        confirmPasswordEye.classList.remove('fa-eye-slash');
                        confirmPasswordEye.classList.add('fa-eye');
                    } else {
                        confirmPasswordEye.classList.remove('fa-eye');
                        confirmPasswordEye.classList.add('fa-eye-slash');
                    }
                });
            }

            // Password strength indicator
            if (passwordInput) {
                passwordInput.addEventListener('input', () => {
                    const password = passwordInput.value;
                    
                    // Length check
                    const lengthCheck = document.getElementById('length-check');
                    const lengthCross = document.getElementById('length-cross');
                    if (password.length >= 6) {
                        lengthCheck.classList.remove('hidden');
                        lengthCross.classList.add('hidden');
                    } else {
                        lengthCheck.classList.add('hidden');
                        lengthCross.classList.remove('hidden');
                    }
                    
                    // Case check
                    const caseCheck = document.getElementById('case-check');
                    const caseCross = document.getElementById('case-cross');
                    if (/(?=.*[a-z])(?=.*[A-Z])/.test(password)) {
                        caseCheck.classList.remove('hidden');
                        caseCross.classList.add('hidden');
                    } else {
                        caseCheck.classList.add('hidden');
                        caseCross.classList.remove('hidden');
                    }
                    
                    // Number check
                    const numberCheck = document.getElementById('number-check');
                    const numberCross = document.getElementById('number-cross');
                    if (/\d/.test(password)) {
                        numberCheck.classList.remove('hidden');
                        numberCross.classList.add('hidden');
                    } else {
                        numberCheck.classList.add('hidden');
                        numberCross.classList.remove('hidden');
                    }
                });
            }

            // Auto-focus first name field
            const firstNameInput = document.getElementById('firstName');
            if (firstNameInput) {
                firstNameInput.focus();
            }

            // Check if user is already logged in
            if (AppState.user) {
                Toast.info('You are already logged in. Redirecting...');
                setTimeout(() => {
                    window.location.href = '/dashboard.html';
                }, 1500);
            }
        });
    </script>
</body>
</html>
