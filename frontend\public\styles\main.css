/* AI Quiz Master - Main Styles */

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #374151;
    background-color: #f9fafb;
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Dark mode styles */
.dark body {
    color: #e5e7eb;
    background-color: #111827;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
}

.dark ::-webkit-scrollbar-track {
    background: #1e293b;
}

::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

.dark ::-webkit-scrollbar-thumb {
    background: #475569;
}

::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

.dark ::-webkit-scrollbar-thumb:hover {
    background: #64748b;
}

/* Navigation Styles */
.nav-link {
    position: relative;
    font-weight: 500;
    transition: all 0.3s ease;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #3b82f6, #8b5cf6);
    transition: width 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    cursor: pointer;
    border: none;
    text-decoration: none;
    font-size: 1rem;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    color: white;
    box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.39);
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 rgba(59, 130, 246, 0.5);
}

.btn-secondary {
    background: #f8fafc;
    color: #475569;
    border: 1px solid #e2e8f0;
}

.dark .btn-secondary {
    background: #1e293b;
    color: #cbd5e1;
    border-color: #475569;
}

.btn-secondary:hover:not(:disabled) {
    background: #f1f5f9;
    transform: translateY(-1px);
}

.dark .btn-secondary:hover:not(:disabled) {
    background: #334155;
}

.btn-danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
}

.btn-danger:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 rgba(239, 68, 68, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.btn-success:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 rgba(16, 185, 129, 0.4);
}

/* Card Styles */
.card {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    border: 1px solid #e5e7eb;
}

.dark .card {
    background: #1f2937;
    border-color: #374151;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3);
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.dark .card:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.4);
}

/* Feature Card Styles */
.feature-card {
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.feature-card:hover::before {
    left: 100%;
}

/* Category Card Styles */
.category-card {
    cursor: pointer;
    position: relative;
    overflow: hidden;
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.dark .category-card {
    background: #1f2937;
}

.category-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    border-color: #3b82f6;
}

.category-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.category-card:hover::after {
    transform: scaleX(1);
}

/* Quiz Card Styles */
.quiz-card {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid #e5e7eb;
}

.dark .quiz-card {
    background: #1f2937;
    border-color: #374151;
}

/* Progress Bar Styles */
.progress-bar {
    width: 100%;
    height: 8px;
    background: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
}

.dark .progress-bar {
    background: #374151;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3b82f6, #8b5cf6);
    border-radius: 4px;
    transition: width 0.3s ease;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

/* Timer Styles */
.timer {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    border-radius: 2rem;
    font-weight: 600;
    font-size: 1.1rem;
    box-shadow: 0 4px 14px 0 rgba(239, 68, 68, 0.39);
}

.timer.warning {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    animation: pulse 1s infinite;
}

.timer.danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    animation: shake 0.5s infinite;
}

/* Score Display */
.score-display {
    display: inline-flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    border-radius: 2rem;
    font-weight: 700;
    font-size: 1.2rem;
    box-shadow: 0 4px 14px 0 rgba(16, 185, 129, 0.39);
}

/* Question Option Styles */
.question-option {
    padding: 1rem 1.5rem;
    border: 2px solid #e5e7eb;
    border-radius: 0.75rem;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
    margin-bottom: 0.75rem;
}

.dark .question-option {
    background: #1f2937;
    border-color: #374151;
}

.question-option:hover {
    border-color: #3b82f6;
    background: #eff6ff;
    transform: translateX(8px);
}

.dark .question-option:hover {
    background: #1e3a8a;
    border-color: #3b82f6;
}

.question-option.selected {
    border-color: #3b82f6;
    background: linear-gradient(135deg, #eff6ff, #dbeafe);
    color: #1e40af;
}

.dark .question-option.selected {
    background: linear-gradient(135deg, #1e3a8a, #1e40af);
    color: #dbeafe;
}

.question-option.correct {
    border-color: #10b981;
    background: linear-gradient(135deg, #ecfdf5, #d1fae5);
    color: #065f46;
}

.dark .question-option.correct {
    background: linear-gradient(135deg, #064e3b, #065f46);
    color: #a7f3d0;
}

.question-option.incorrect {
    border-color: #ef4444;
    background: linear-gradient(135deg, #fef2f2, #fecaca);
    color: #991b1b;
}

.dark .question-option.incorrect {
    background: linear-gradient(135deg, #7f1d1d, #991b1b);
    color: #fca5a5;
}

/* Toast Notification Styles */
.toast {
    padding: 1rem 1.5rem;
    border-radius: 0.5rem;
    color: white;
    font-weight: 500;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    transform: translateX(100%);
    transition: transform 0.3s ease;
    max-width: 400px;
    position: relative;
    overflow: hidden;
}

.toast.show {
    transform: translateX(0);
}

.toast.success {
    background: linear-gradient(135deg, #10b981, #059669);
}

.toast.error {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.toast.warning {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.toast.info {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.toast::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    animation: toast-progress 5s linear;
}

/* Loading Spinner */
.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e5e7eb;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.dark .spinner {
    border-color: #374151;
    border-top-color: #3b82f6;
}

/* Form Styles */
.form-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.dark .form-input {
    background: #1f2937;
    border-color: #374151;
    color: #e5e7eb;
}

.form-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input.error {
    border-color: #ef4444;
}

.form-input.error:focus {
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* Badge Styles */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 9999px;
}

.badge.primary {
    background: #dbeafe;
    color: #1e40af;
}

.dark .badge.primary {
    background: #1e3a8a;
    color: #dbeafe;
}

.badge.success {
    background: #d1fae5;
    color: #065f46;
}

.dark .badge.success {
    background: #064e3b;
    color: #a7f3d0;
}

.badge.warning {
    background: #fef3c7;
    color: #92400e;
}

.dark .badge.warning {
    background: #78350f;
    color: #fcd34d;
}

.badge.danger {
    background: #fecaca;
    color: #991b1b;
}

.dark .badge.danger {
    background: #7f1d1d;
    color: #fca5a5;
}

/* Responsive Design */
@media (max-width: 768px) {
    .category-card {
        padding: 1rem;
    }
    
    .quiz-card {
        padding: 1.5rem;
    }
    
    .question-option {
        padding: 0.75rem 1rem;
    }
    
    .btn {
        padding: 0.625rem 1.25rem;
        font-size: 0.875rem;
    }
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(135deg, #3b82f6, #8b5cf6, #ec4899);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
}

.bg-gradient-success {
    background: linear-gradient(135deg, #10b981, #059669);
}

.bg-gradient-warning {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.bg-gradient-danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}
