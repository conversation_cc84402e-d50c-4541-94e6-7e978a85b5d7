const OpenAI = require('openai');

/**
 * ChatGPT Service for AI-powered question generation and evaluation
 */
class ChatGPTService {
  constructor() {
    if (!process.env.OPENAI_API_KEY) {
      console.warn('⚠️ OpenAI API key not found. ChatGPT features will be disabled.');
      this.client = null;
      return;
    }

    this.client = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });

    this.defaultModel = 'gpt-3.5-turbo';
    this.maxTokens = 1000;
    this.temperature = 0.7;
  }

  /**
   * Check if ChatGPT service is available
   */
  isAvailable() {
    return this.client !== null;
  }

  /**
   * Generate quiz questions based on category and difficulty
   */
  async generateQuestions(category, difficulty, count = 5, customPrompt = null) {
    if (!this.isAvailable()) {
      throw new Error('ChatGPT service is not available. Please check your API key.');
    }

    try {
      const prompt = customPrompt || this.buildQuestionPrompt(category, difficulty, count);
      
      const response = await this.client.chat.completions.create({
        model: this.defaultModel,
        messages: [
          {
            role: 'system',
            content: 'You are an expert quiz creator. Generate high-quality, educational quiz questions with accurate answers and explanations.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: this.maxTokens,
        temperature: this.temperature,
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No response received from ChatGPT');
      }

      return this.parseQuestionResponse(content, category, difficulty);
    } catch (error) {
      console.error('ChatGPT question generation error:', error);
      throw new Error(`Failed to generate questions: ${error.message}`);
    }
  }

  /**
   * Evaluate user's answer and provide feedback
   */
  async evaluateAnswer(question, userAnswer, correctAnswer) {
    if (!this.isAvailable()) {
      throw new Error('ChatGPT service is not available. Please check your API key.');
    }

    try {
      const prompt = this.buildEvaluationPrompt(question, userAnswer, correctAnswer);
      
      const response = await this.client.chat.completions.create({
        model: this.defaultModel,
        messages: [
          {
            role: 'system',
            content: 'You are an educational AI assistant. Provide constructive feedback on quiz answers, explaining why answers are correct or incorrect.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 300,
        temperature: 0.5,
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No response received from ChatGPT');
      }

      return this.parseEvaluationResponse(content);
    } catch (error) {
      console.error('ChatGPT answer evaluation error:', error);
      throw new Error(`Failed to evaluate answer: ${error.message}`);
    }
  }

  /**
   * Generate explanation for a question
   */
  async generateExplanation(question, correctAnswer) {
    if (!this.isAvailable()) {
      throw new Error('ChatGPT service is not available. Please check your API key.');
    }

    try {
      const prompt = `Provide a clear, educational explanation for this quiz question:

Question: ${question}
Correct Answer: ${correctAnswer}

Please explain why this is the correct answer in 2-3 sentences that would help a student understand the concept.`;

      const response = await this.client.chat.completions.create({
        model: this.defaultModel,
        messages: [
          {
            role: 'system',
            content: 'You are an educational expert. Provide clear, concise explanations that help students learn.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 200,
        temperature: 0.5,
      });

      return response.choices[0]?.message?.content?.trim() || '';
    } catch (error) {
      console.error('ChatGPT explanation generation error:', error);
      return '';
    }
  }

  /**
   * Build prompt for question generation
   */
  buildQuestionPrompt(category, difficulty, count) {
    return `Generate ${count} multiple-choice quiz questions about ${category} with ${difficulty} difficulty level.

Requirements:
- Each question should have 4 options (A, B, C, D)
- Only one correct answer per question
- Include a brief explanation for the correct answer
- Questions should be educational and accurate
- Vary the question types within the category

Format your response as a JSON array with this structure:
[
  {
    "question": "Question text here?",
    "options": [
      {"text": "Option A", "isCorrect": false},
      {"text": "Option B", "isCorrect": true},
      {"text": "Option C", "isCorrect": false},
      {"text": "Option D", "isCorrect": false}
    ],
    "explanation": "Brief explanation of why the correct answer is right",
    "difficulty": "${difficulty}",
    "category": "${category}"
  }
]

Please ensure the JSON is valid and properly formatted.`;
  }

  /**
   * Build prompt for answer evaluation
   */
  buildEvaluationPrompt(question, userAnswer, correctAnswer) {
    return `Evaluate this quiz answer and provide feedback:

Question: ${question}
User's Answer: ${userAnswer}
Correct Answer: ${correctAnswer}

Please provide:
1. Whether the answer is correct or incorrect
2. Brief feedback explaining why
3. A confidence score (0-100) for your evaluation
4. One helpful tip or suggestion for improvement

Format your response as JSON:
{
  "isCorrect": boolean,
  "feedback": "Your feedback here",
  "confidence": number,
  "suggestion": "Helpful tip here"
}`;
  }

  /**
   * Parse question generation response
   */
  parseQuestionResponse(content, category, difficulty) {
    try {
      // Try to extract JSON from the response
      const jsonMatch = content.match(/\[[\s\S]*\]/);
      if (!jsonMatch) {
        throw new Error('No valid JSON found in response');
      }

      const questions = JSON.parse(jsonMatch[0]);
      
      return questions.map(q => ({
        question: q.question,
        options: q.options || [],
        correctAnswer: q.options?.find(opt => opt.isCorrect)?.text || '',
        explanation: q.explanation || '',
        category: category,
        difficulty: difficulty,
        source: 'chatgpt',
        type: 'multiple-choice'
      }));
    } catch (error) {
      console.error('Error parsing ChatGPT response:', error);
      throw new Error('Failed to parse question response from ChatGPT');
    }
  }

  /**
   * Parse evaluation response
   */
  parseEvaluationResponse(content) {
    try {
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        // Fallback to simple text response
        return {
          feedback: content.trim(),
          confidence: 80,
          suggestions: []
        };
      }

      const evaluation = JSON.parse(jsonMatch[0]);
      return {
        feedback: evaluation.feedback || content.trim(),
        confidence: evaluation.confidence || 80,
        suggestions: evaluation.suggestion ? [evaluation.suggestion] : []
      };
    } catch (error) {
      console.error('Error parsing ChatGPT evaluation:', error);
      return {
        feedback: content.trim(),
        confidence: 70,
        suggestions: []
      };
    }
  }
}

module.exports = new ChatGPTService();
