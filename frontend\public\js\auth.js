/**
 * AI Quiz Master - Authentication JavaScript
 * Handles user authentication, registration, and profile management
 */

// Authentication Manager
const AuthManager = {
    // Login user
    async login(email, password) {
        try {
            LoadingManager.show('Signing you in...');

            const response = await Utils.apiCall('/auth/login', {
                method: 'POST',
                body: JSON.stringify({ email, password })
            });

            // Store user data and token
            AppState.token = response.data.token;
            AppState.user = response.data.user;
            
            Utils.setStorage(CONFIG.STORAGE_KEYS.TOKEN, AppState.token);
            Utils.setStorage(CONFIG.STORAGE_KEYS.USER, AppState.user);

            // Update navigation
            NavigationManager.updateNavigation();

            LoadingManager.hide();
            Toast.success('Login successful! Welcome back.');

            // Redirect to dashboard or intended page
            const redirectUrl = Utils.getStorage('redirectAfterLogin') || '/dashboard.html';
            Utils.removeStorage('redirectAfterLogin');
            
            setTimeout(() => {
                window.location.href = redirectUrl;
            }, 1500);

            return response;
        } catch (error) {
            LoadingManager.hide();
            Toast.error(error.message || 'Login failed. Please try again.');
            throw error;
        }
    },

    // Register new user
    async register(userData) {
        try {
            LoadingManager.show('Creating your account...');

            const response = await Utils.apiCall('/auth/register', {
                method: 'POST',
                body: JSON.stringify(userData)
            });

            // Store user data and token
            AppState.token = response.data.token;
            AppState.user = response.data.user;
            
            Utils.setStorage(CONFIG.STORAGE_KEYS.TOKEN, AppState.token);
            Utils.setStorage(CONFIG.STORAGE_KEYS.USER, AppState.user);

            // Update navigation
            NavigationManager.updateNavigation();

            LoadingManager.hide();
            Toast.success('Account created successfully! Welcome to AI Quiz Master.');

            // Redirect to dashboard
            setTimeout(() => {
                window.location.href = '/dashboard.html';
            }, 1500);

            return response;
        } catch (error) {
            LoadingManager.hide();
            Toast.error(error.message || 'Registration failed. Please try again.');
            throw error;
        }
    },

    // Update user profile
    async updateProfile(profileData) {
        try {
            LoadingManager.show('Updating profile...');

            const response = await Utils.apiCall('/auth/profile', {
                method: 'PUT',
                body: JSON.stringify(profileData)
            });

            // Update stored user data
            AppState.user = response.data.user;
            Utils.setStorage(CONFIG.STORAGE_KEYS.USER, AppState.user);

            // Update navigation
            NavigationManager.updateNavigation();

            LoadingManager.hide();
            Toast.success('Profile updated successfully!');

            return response;
        } catch (error) {
            LoadingManager.hide();
            Toast.error(error.message || 'Failed to update profile.');
            throw error;
        }
    },

    // Change password
    async changePassword(currentPassword, newPassword) {
        try {
            LoadingManager.show('Changing password...');

            const response = await Utils.apiCall('/auth/change-password', {
                method: 'PUT',
                body: JSON.stringify({
                    currentPassword,
                    newPassword
                })
            });

            LoadingManager.hide();
            Toast.success('Password changed successfully!');

            return response;
        } catch (error) {
            LoadingManager.hide();
            Toast.error(error.message || 'Failed to change password.');
            throw error;
        }
    },

    // Validate form data
    validateLoginForm(email, password) {
        const errors = [];

        if (!email || !email.trim()) {
            errors.push('Email is required');
        } else if (!this.isValidEmail(email)) {
            errors.push('Please enter a valid email address');
        }

        if (!password || !password.trim()) {
            errors.push('Password is required');
        }

        return errors;
    },

    validateRegistrationForm(formData) {
        const errors = [];

        if (!formData.firstName || !formData.firstName.trim()) {
            errors.push('First name is required');
        }

        if (!formData.lastName || !formData.lastName.trim()) {
            errors.push('Last name is required');
        }

        if (!formData.username || !formData.username.trim()) {
            errors.push('Username is required');
        } else if (formData.username.length < 3) {
            errors.push('Username must be at least 3 characters long');
        } else if (!/^[a-zA-Z0-9_]+$/.test(formData.username)) {
            errors.push('Username can only contain letters, numbers, and underscores');
        }

        if (!formData.email || !formData.email.trim()) {
            errors.push('Email is required');
        } else if (!this.isValidEmail(formData.email)) {
            errors.push('Please enter a valid email address');
        }

        if (!formData.password || !formData.password.trim()) {
            errors.push('Password is required');
        } else if (formData.password.length < 6) {
            errors.push('Password must be at least 6 characters long');
        } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(formData.password)) {
            errors.push('Password must contain at least one lowercase letter, one uppercase letter, and one number');
        }

        if (formData.password !== formData.confirmPassword) {
            errors.push('Passwords do not match');
        }

        return errors;
    },

    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },

    // Show form errors
    showFormErrors(errors, containerId = 'form-errors') {
        const errorContainer = document.getElementById(containerId);
        if (!errorContainer) return;

        if (errors.length === 0) {
            errorContainer.classList.add('hidden');
            return;
        }

        errorContainer.innerHTML = `
            <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                <div class="flex">
                    <i class="fas fa-exclamation-circle text-red-400 mt-0.5"></i>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                            Please correct the following errors:
                        </h3>
                        <ul class="mt-2 text-sm text-red-700 dark:text-red-300 list-disc list-inside">
                            ${errors.map(error => `<li>${error}</li>`).join('')}
                        </ul>
                    </div>
                </div>
            </div>
        `;
        errorContainer.classList.remove('hidden');
    },

    // Clear form errors
    clearFormErrors(containerId = 'form-errors') {
        const errorContainer = document.getElementById(containerId);
        if (errorContainer) {
            errorContainer.classList.add('hidden');
            errorContainer.innerHTML = '';
        }
    }
};

// Login Form Handler
const LoginForm = {
    init() {
        const form = document.getElementById('login-form');
        if (!form) return;

        form.addEventListener('submit', this.handleSubmit.bind(this));
        
        // Add real-time validation
        const emailInput = document.getElementById('email');
        const passwordInput = document.getElementById('password');
        
        if (emailInput) {
            emailInput.addEventListener('blur', this.validateEmail.bind(this));
        }
        
        if (passwordInput) {
            passwordInput.addEventListener('input', this.validatePassword.bind(this));
        }
    },

    async handleSubmit(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const email = formData.get('email');
        const password = formData.get('password');

        // Clear previous errors
        AuthManager.clearFormErrors();

        // Validate form
        const errors = AuthManager.validateLoginForm(email, password);
        if (errors.length > 0) {
            AuthManager.showFormErrors(errors);
            return;
        }

        try {
            await AuthManager.login(email, password);
        } catch (error) {
            // Error is already handled in AuthManager.login
        }
    },

    validateEmail() {
        const emailInput = document.getElementById('email');
        const email = emailInput.value.trim();
        
        if (email && !AuthManager.isValidEmail(email)) {
            emailInput.classList.add('error');
            this.showFieldError('email', 'Please enter a valid email address');
        } else {
            emailInput.classList.remove('error');
            this.clearFieldError('email');
        }
    },

    validatePassword() {
        const passwordInput = document.getElementById('password');
        const password = passwordInput.value;
        
        if (password && password.length < 6) {
            passwordInput.classList.add('error');
            this.showFieldError('password', 'Password must be at least 6 characters long');
        } else {
            passwordInput.classList.remove('error');
            this.clearFieldError('password');
        }
    },

    showFieldError(fieldName, message) {
        let errorEl = document.getElementById(`${fieldName}-error`);
        if (!errorEl) {
            errorEl = document.createElement('div');
            errorEl.id = `${fieldName}-error`;
            errorEl.className = 'text-red-500 text-sm mt-1';
            
            const field = document.getElementById(fieldName);
            if (field && field.parentNode) {
                field.parentNode.appendChild(errorEl);
            }
        }
        errorEl.textContent = message;
    },

    clearFieldError(fieldName) {
        const errorEl = document.getElementById(`${fieldName}-error`);
        if (errorEl) {
            errorEl.remove();
        }
    }
};

// Registration Form Handler
const RegistrationForm = {
    init() {
        const form = document.getElementById('register-form');
        if (!form) return;

        form.addEventListener('submit', this.handleSubmit.bind(this));
        
        // Add real-time validation
        const inputs = ['firstName', 'lastName', 'username', 'email', 'password', 'confirmPassword'];
        inputs.forEach(inputName => {
            const input = document.getElementById(inputName);
            if (input) {
                input.addEventListener('blur', () => this.validateField(inputName));
            }
        });

        // Password confirmation validation
        const confirmPasswordInput = document.getElementById('confirmPassword');
        if (confirmPasswordInput) {
            confirmPasswordInput.addEventListener('input', this.validatePasswordConfirmation.bind(this));
        }
    },

    async handleSubmit(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const userData = {
            firstName: formData.get('firstName'),
            lastName: formData.get('lastName'),
            username: formData.get('username'),
            email: formData.get('email'),
            password: formData.get('password'),
            confirmPassword: formData.get('confirmPassword')
        };

        // Clear previous errors
        AuthManager.clearFormErrors();

        // Validate form
        const errors = AuthManager.validateRegistrationForm(userData);
        if (errors.length > 0) {
            AuthManager.showFormErrors(errors);
            return;
        }

        // Remove confirmPassword before sending to API
        delete userData.confirmPassword;

        try {
            await AuthManager.register(userData);
        } catch (error) {
            // Error is already handled in AuthManager.register
        }
    },

    validateField(fieldName) {
        const input = document.getElementById(fieldName);
        const value = input.value.trim();
        
        let isValid = true;
        let errorMessage = '';

        switch (fieldName) {
            case 'firstName':
            case 'lastName':
                if (!value) {
                    isValid = false;
                    errorMessage = `${fieldName === 'firstName' ? 'First' : 'Last'} name is required`;
                }
                break;
            
            case 'username':
                if (!value) {
                    isValid = false;
                    errorMessage = 'Username is required';
                } else if (value.length < 3) {
                    isValid = false;
                    errorMessage = 'Username must be at least 3 characters long';
                } else if (!/^[a-zA-Z0-9_]+$/.test(value)) {
                    isValid = false;
                    errorMessage = 'Username can only contain letters, numbers, and underscores';
                }
                break;
            
            case 'email':
                if (!value) {
                    isValid = false;
                    errorMessage = 'Email is required';
                } else if (!AuthManager.isValidEmail(value)) {
                    isValid = false;
                    errorMessage = 'Please enter a valid email address';
                }
                break;
            
            case 'password':
                if (!value) {
                    isValid = false;
                    errorMessage = 'Password is required';
                } else if (value.length < 6) {
                    isValid = false;
                    errorMessage = 'Password must be at least 6 characters long';
                } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(value)) {
                    isValid = false;
                    errorMessage = 'Password must contain at least one lowercase letter, one uppercase letter, and one number';
                }
                break;
        }

        if (isValid) {
            input.classList.remove('error');
            LoginForm.clearFieldError(fieldName);
        } else {
            input.classList.add('error');
            LoginForm.showFieldError(fieldName, errorMessage);
        }

        return isValid;
    },

    validatePasswordConfirmation() {
        const passwordInput = document.getElementById('password');
        const confirmPasswordInput = document.getElementById('confirmPassword');
        
        if (!passwordInput || !confirmPasswordInput) return;

        const password = passwordInput.value;
        const confirmPassword = confirmPasswordInput.value;

        if (confirmPassword && password !== confirmPassword) {
            confirmPasswordInput.classList.add('error');
            LoginForm.showFieldError('confirmPassword', 'Passwords do not match');
        } else {
            confirmPasswordInput.classList.remove('error');
            LoginForm.clearFieldError('confirmPassword');
        }
    }
};

// Initialize forms when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    LoginForm.init();
    RegistrationForm.init();
});

// Export for global access
window.AuthManager = AuthManager;
window.LoginForm = LoginForm;
window.RegistrationForm = RegistrationForm;
