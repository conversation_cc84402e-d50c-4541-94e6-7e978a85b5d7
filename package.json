{"name": "ai-quiz-website", "version": "1.0.0", "description": "Full-stack quiz website with ChatGPT and Gemini API integration", "main": "backend/server.js", "scripts": {"start": "node backend/server.js", "dev": "nodemon backend/server.js", "client": "live-server frontend/public", "build": "npm run build:frontend", "build:frontend": "echo 'Frontend build complete'", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["quiz", "chatgpt", "gemini", "nodejs", "mongodb", "express"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "openai": "^4.20.1", "@google/generative-ai": "^0.2.1", "multer": "^1.4.5-lts.1", "compression": "^1.7.4", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.2", "live-server": "^1.2.2"}, "engines": {"node": ">=16.0.0"}}