const mongoose = require('mongoose');

/**
 * Question Schema
 */
const questionSchema = new mongoose.Schema({
  question: {
    type: String,
    required: [true, 'Question text is required'],
    trim: true,
    maxlength: [500, 'Question cannot exceed 500 characters']
  },
  options: [{
    text: {
      type: String,
      required: true,
      trim: true,
      maxlength: [200, 'Option text cannot exceed 200 characters']
    },
    isCorrect: {
      type: Boolean,
      default: false
    }
  }],
  correctAnswer: {
    type: String,
    required: [true, 'Correct answer is required']
  },
  explanation: {
    type: String,
    maxlength: [300, 'Explanation cannot exceed 300 characters']
  },
  category: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category',
    required: [true, 'Category is required']
  },
  difficulty: {
    type: String,
    enum: ['easy', 'medium', 'hard'],
    required: [true, 'Difficulty level is required']
  },
  type: {
    type: String,
    enum: ['multiple-choice', 'true-false', 'fill-blank'],
    default: 'multiple-choice'
  },
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  timeLimit: {
    type: Number,
    default: 30, // seconds
    min: [10, 'Time limit must be at least 10 seconds'],
    max: [300, 'Time limit cannot exceed 300 seconds']
  },
  points: {
    type: Number,
    default: 10,
    min: [1, 'Points must be at least 1'],
    max: [100, 'Points cannot exceed 100']
  },
  source: {
    type: String,
    enum: ['manual', 'chatgpt', 'gemini'],
    default: 'manual'
  },
  aiPrompt: {
    type: String,
    maxlength: [500, 'AI prompt cannot exceed 500 characters']
  },
  stats: {
    totalAttempts: {
      type: Number,
      default: 0
    },
    correctAttempts: {
      type: Number,
      default: 0
    },
    averageTime: {
      type: Number,
      default: 0
    },
    difficultyRating: {
      type: Number,
      default: 0,
      min: 0,
      max: 5
    }
  },
  isActive: {
    type: Boolean,
    default: true
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Indexes for better query performance
questionSchema.index({ category: 1, difficulty: 1 });
questionSchema.index({ isActive: 1 });
questionSchema.index({ tags: 1 });
questionSchema.index({ 'stats.difficultyRating': -1 });

// Pre-save middleware
questionSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  
  // Ensure at least one correct answer for multiple choice
  if (this.type === 'multiple-choice') {
    const hasCorrectAnswer = this.options.some(option => option.isCorrect);
    if (!hasCorrectAnswer) {
      return next(new Error('At least one option must be marked as correct'));
    }
  }
  
  next();
});

// Static method to get random questions by category and difficulty
questionSchema.statics.getRandomQuestions = function(categoryId, difficulty, limit = 10) {
  const matchQuery = {
    category: categoryId,
    isActive: true
  };
  
  if (difficulty && difficulty !== 'mixed') {
    matchQuery.difficulty = difficulty;
  }
  
  return this.aggregate([
    { $match: matchQuery },
    { $sample: { size: limit } },
    {
      $lookup: {
        from: 'categories',
        localField: 'category',
        foreignField: '_id',
        as: 'categoryInfo'
      }
    }
  ]);
};

// Static method to get questions by category with pagination
questionSchema.statics.getQuestionsByCategory = function(categoryId, page = 1, limit = 20) {
  const skip = (page - 1) * limit;
  
  return this.find({ category: categoryId, isActive: true })
    .populate('category', 'name slug')
    .populate('createdBy', 'username firstName lastName')
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit);
};

// Instance method to update stats
questionSchema.methods.updateStats = function(isCorrect, timeSpent) {
  this.stats.totalAttempts += 1;
  
  if (isCorrect) {
    this.stats.correctAttempts += 1;
  }
  
  // Update average time
  const totalTime = (this.stats.averageTime * (this.stats.totalAttempts - 1)) + timeSpent;
  this.stats.averageTime = totalTime / this.stats.totalAttempts;
  
  // Update difficulty rating based on success rate
  const successRate = this.stats.correctAttempts / this.stats.totalAttempts;
  this.stats.difficultyRating = 5 - (successRate * 5); // Inverse relationship
  
  return this.save();
};

// Virtual for success rate
questionSchema.virtual('successRate').get(function() {
  if (this.stats.totalAttempts === 0) return 0;
  return (this.stats.correctAttempts / this.stats.totalAttempts) * 100;
});

// Ensure virtual fields are serialized
questionSchema.set('toJSON', {
  virtuals: true
});

module.exports = mongoose.model('Question', questionSchema);
